"""
头条号平台并发处理集成模块
提供与现有UI系统的集成接口
"""

import threading
from typing import List, Dict, Any, Optional, Callable

# 尝试相对导入，如果失败则使用绝对导入
try:
    from .concurrency.manager import ToutiaoConcurrentManager
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    from concurrency.manager import ToutiaoConcurrentManager


class ToutiaoConcurrentIntegration:
    """头条号并发处理集成类"""

    def __init__(self, config_manager, log_callback: Callable = None):
        """
        初始化头条号并发处理集成

        Args:
            config_manager: 配置管理器
            log_callback: 日志回调函数
        """
        self.config_manager = config_manager
        self.log_callback = log_callback
        self.concurrent_manager = None
        self.is_running = False

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def start_concurrent_processing(self, accounts: List[str], 
                                  progress_callback: Optional[Callable] = None) -> bool:
        """
        启动头条号并发处理

        Args:
            accounts: 账号列表
            progress_callback: 进度回调函数

        Returns:
            是否成功启动
        """
        if self.is_running:
            self.log("头条号并发处理已在运行中")
            return False

        if not accounts:
            self.log("没有指定要处理的头条号账号")
            return False

        try:
            # 以“当前平台”的存稿设置为唯一来源（统一）
            current_platform = self.config_manager.get_current_platform()

            # 从当前平台的存稿设置读取目录与选项
            account_dir = self.config_manager.get_path("account_dir", platform=current_platform)
            processed_dir = self.config_manager.get_path("processed_dir", platform=current_platform)
            processed_covers_dir = self.config_manager.get_path("processed_covers_dir", platform=current_platform)
            screenshots_dir = self.config_manager.get_path("screenshots_dir", platform=current_platform)

            # 运行选项（当前平台）
            archive_completed = self.config_manager.get("archive_completed", True, platform=current_platform)
            headless_mode = self.config_manager.get("headless_mode", False, platform=current_platform)
            draft_limit = self.config_manager.get("draft_limit", 0, platform=current_platform)
            loop_limit = self.config_manager.get("loop_limit", 0, platform=current_platform)

            # 并发数：仅使用存稿设置中的 concurrent_accounts（当前平台）
            max_workers = self.config_manager.get("concurrent_accounts", 3, platform=current_platform)
            try:
                max_workers = int(max_workers)
            except Exception:
                max_workers = 3

            random_video_allocation = self.config_manager.get("random_video_allocation", True, platform="toutiao")

            # 创建并发管理器
            self.concurrent_manager = ToutiaoConcurrentManager(
                account_dir=account_dir,
                processed_dir=processed_dir,
                processed_covers_dir=processed_covers_dir,
                archive_completed=archive_completed,
                headless_mode=headless_mode,
                draft_limit=draft_limit,
                loop_limit=loop_limit,
                log_callback=self.log_callback,
                screenshots_dir=screenshots_dir,
                max_workers=max_workers,
                random_video_allocation=random_video_allocation
            )

            # 设置进度回调
            if progress_callback:
                self.concurrent_manager.set_progress_callback(progress_callback)

            # 启动并发处理
            success = self.concurrent_manager.start(accounts)
            
            if success:
                self.is_running = True
                self.log(f"✅ 头条号并发处理启动成功，处理 {len(accounts)} 个账号")
                
                # 启动监控线程
                threading.Thread(target=self._monitor_completion, daemon=True).start()
            else:
                self.log("❌ 头条号并发处理启动失败")

            return success

        except Exception as e:
            self.log(f"❌ 启动头条号并发处理时发生异常: {str(e)}")
            return False

    def stop_concurrent_processing(self) -> None:
        """停止头条号并发处理"""
        if not self.is_running:
            return

        try:
            if self.concurrent_manager:
                self.concurrent_manager.stop()
            
            self.is_running = False
            self.log("头条号并发处理已停止")

        except Exception as e:
            self.log(f"停止头条号并发处理时发生异常: {str(e)}")

    def get_progress(self) -> Dict[str, Any]:
        """
        获取处理进度

        Returns:
            进度信息字典
        """
        if self.concurrent_manager:
            return self.concurrent_manager.get_progress()
        else:
            return {
                "is_running": False,
                "account_progress": {},
                "total_accounts": 0,
                "completed_accounts": 0
            }

    def is_processing(self) -> bool:
        """
        检查是否正在处理

        Returns:
            是否正在处理
        """
        return self.is_running

    def _monitor_completion(self) -> None:
        """监控处理完成状态"""
        try:
            while self.is_running and self.concurrent_manager:
                progress = self.concurrent_manager.get_progress()
                
                # 检查是否所有账号都已完成
                if not progress["is_running"]:
                    self.is_running = False
                    self.log("🎉 头条号并发处理全部完成")
                    break
                
                # 等待一段时间再检查
                threading.Event().wait(3)

        except Exception as e:
            self.log(f"监控头条号并发处理完成状态时发生异常: {str(e)}")


class ToutiaoTaskManager:
    """头条号任务管理器 - 与现有UI系统集成"""

    def __init__(self, parent_ui):
        """
        初始化头条号任务管理器

        Args:
            parent_ui: 父UI对象
        """
        self.parent_ui = parent_ui
        self.integration = ToutiaoConcurrentIntegration(
            config_manager=parent_ui.config_manager,
            log_callback=parent_ui.log
        )

    def start_single_account_task(self, account: str) -> bool:
        """
        启动单个头条号账号任务

        Args:
            account: 账号名称

        Returns:
            是否成功启动
        """
        try:
            # 使用头条号处理器处理单个账号
            try:
                from .processor import ToutiaoDraftProcessor
            except ImportError:
                # 如果相对导入失败，尝试绝对导入
                from 网易号存稿.platforms.toutiao.processor import ToutiaoDraftProcessor
            
            processor = ToutiaoDraftProcessor(
                account_dir=self.parent_ui.config_manager.get_path("account_dir"),
                processed_dir=self.parent_ui.config_manager.get_path("video_dir"),
                processed_covers_dir=self.parent_ui.config_manager.get_path("cover_dir"),
                archive_completed=self.parent_ui.config_manager.get("archive_completed", True, platform="toutiao"),
                headless_mode=self.parent_ui.config_manager.get("headless_mode", False, platform="toutiao"),
                draft_limit=self.parent_ui.config_manager.get("draft_limit", 0, platform="toutiao"),
                loop_limit=self.parent_ui.config_manager.get("loop_limit", 0, platform="toutiao"),
                log_callback=self.parent_ui.log,
                screenshots_dir=self.parent_ui.config_manager.get_path("screenshots_dir"),
                random_video_allocation=self.parent_ui.config_manager.get("random_video_allocation", True, platform="toutiao")
            )

            # 在后台线程中处理
            def process_thread():
                try:
                    self.parent_ui.log(f"🚀 开始处理头条号账号: {account}")

                    # 注意：单账号处理模式下，处理器会自己创建和管理driver
                    # 不需要在这里手动设置driver
                    success, _ = processor.process_account(account)

                    if success:
                        self.parent_ui.log(f"✅ 头条号账号 {account} 处理完成")
                    else:
                        self.parent_ui.log(f"❌ 头条号账号 {account} 处理失败")

                except Exception as e:
                    self.parent_ui.log(f"❌ 处理头条号账号 {account} 时发生异常: {str(e)}")
                    import traceback
                    traceback.print_exc()

            threading.Thread(target=process_thread, daemon=True).start()
            return True

        except Exception as e:
            self.parent_ui.log(f"❌ 启动头条号单账号任务时发生异常: {str(e)}")
            return False

    def start_concurrent_task(self, accounts: List[str]) -> bool:
        """
        启动头条号并发任务

        Args:
            accounts: 账号列表

        Returns:
            是否成功启动
        """
        def progress_callback(progress_data):
            """进度回调函数"""
            try:
                # 更新UI进度显示
                if hasattr(self.parent_ui, 'update_concurrent_progress'):
                    self.parent_ui.update_concurrent_progress(progress_data)
            except Exception as e:
                self.parent_ui.log(f"更新头条号并发进度时发生异常: {str(e)}")

        return self.integration.start_concurrent_processing(accounts, progress_callback)

    def stop_concurrent_task(self) -> None:
        """停止头条号并发任务"""
        self.integration.stop_concurrent_processing()

    def get_progress(self) -> Dict[str, Any]:
        """获取处理进度"""
        return self.integration.get_progress()

    def is_processing(self) -> bool:
        """检查是否正在处理"""
        return self.integration.is_processing()
