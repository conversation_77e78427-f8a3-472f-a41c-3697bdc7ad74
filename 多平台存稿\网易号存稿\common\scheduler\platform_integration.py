"""
平台集成器模块
负责与现有的数据查询和存稿功能进行集成
"""

import time
from typing import Dict, Any, List, Optional, Callable

from .time_utils import TimeUtils


class PlatformIntegration:
    """平台集成器，用于调用现有的平台功能"""
    
    def __init__(self, data_query_manager=None, draft_task_manager=None, logger: Optional[Callable] = None):
        """
        初始化平台集成器
        
        Args:
            data_query_manager: 数据查询管理器
            draft_task_manager: 存稿任务管理器
            logger: 日志记录函数
        """
        self.data_query_manager = data_query_manager
        self.draft_task_manager = draft_task_manager
        self.logger = logger or print
        
        # 支持的平台列表
        self.supported_platforms = ["netease", "toutiao", "dayu"]
    
    def set_managers(self, data_query_manager=None, draft_task_manager=None):
        """设置管理器实例"""
        if data_query_manager:
            self.data_query_manager = data_query_manager
        if draft_task_manager:
            self.draft_task_manager = draft_task_manager
    
    def query_all_accounts_data(self, platform: str) -> Dict[str, Any]:
        """
        查询所有账号数据
        
        Args:
            platform: 平台名称
            
        Returns:
            查询结果
        """
        if not self.data_query_manager:
            return {
                'success': False,
                'error_message': '数据查询管理器未初始化',
                'data': None
            }
        
        if platform not in self.supported_platforms:
            return {
                'success': False,
                'error_message': f'不支持的平台: {platform}',
                'data': None
            }
        
        try:
            self.logger(f"🔍 开始查询 {platform} 平台所有账号数据")
            
            # 切换到指定平台
            original_platform = getattr(self.data_query_manager.parent_ui, 'current_platform', 'netease')
            self.data_query_manager.parent_ui.current_platform = platform
            
            # 记录开始时间
            start_time = time.time()
            
            # 调用数据查询管理器的查询方法
            self.data_query_manager.start_data_query()
            
            # 等待查询完成（简化实现，实际应该使用回调或事件）
            max_wait_time = 300  # 最大等待5分钟
            wait_time = 0
            check_interval = 2
            
            while self.data_query_manager.is_querying and wait_time < max_wait_time:
                time.sleep(check_interval)
                wait_time += check_interval
            
            # 恢复原平台
            self.data_query_manager.parent_ui.current_platform = original_platform
            
            duration = time.time() - start_time
            
            if wait_time >= max_wait_time:
                return {
                    'success': False,
                    'error_message': '查询超时',
                    'data': {'duration': duration}
                }
            
            # 获取查询结果（从UI状态或统计信息中获取）
            success_count = getattr(self.data_query_manager.parent_ui, 'success_count', 0)
            failure_count = getattr(self.data_query_manager.parent_ui, 'failure_count', 0)
            total_accounts = success_count + failure_count
            
            self.logger(f"✅ {platform} 平台数据查询完成: 成功 {success_count}, 失败 {failure_count}")
            
            return {
                'success': True,
                'error_message': None,
                'data': {
                    'platform': platform,
                    'accounts_processed': total_accounts,
                    'success_count': success_count,
                    'failure_count': failure_count,
                    'duration': duration
                }
            }
            
        except Exception as e:
            self.logger(f"❌ 查询 {platform} 平台数据异常: {str(e)}")
            return {
                'success': False,
                'error_message': f'查询异常: {str(e)}',
                'data': None
            }
    
    def query_single_account_data(self, platform: str, account: str) -> Dict[str, Any]:
        """
        查询单个账号数据
        
        Args:
            platform: 平台名称
            account: 账号名称
            
        Returns:
            查询结果
        """
        if not self.data_query_manager:
            return {
                'success': False,
                'error_message': '数据查询管理器未初始化',
                'data': None
            }
        
        if platform not in self.supported_platforms:
            return {
                'success': False,
                'error_message': f'不支持的平台: {platform}',
                'data': None
            }
        
        try:
            self.logger(f"🔍 开始查询 {platform} 平台账号 {account} 的数据")
            
            # 切换到指定平台
            original_platform = getattr(self.data_query_manager.parent_ui, 'current_platform', 'netease')
            self.data_query_manager.parent_ui.current_platform = platform
            
            # 记录开始时间
            start_time = time.time()
            
            # 调用单账号查询方法
            self.data_query_manager.start_single_account_query(account)
            
            # 等待查询完成
            max_wait_time = 120  # 单账号查询最大等待2分钟
            wait_time = 0
            check_interval = 1
            
            while self.data_query_manager.is_querying and wait_time < max_wait_time:
                time.sleep(check_interval)
                wait_time += check_interval
            
            # 恢复原平台
            self.data_query_manager.parent_ui.current_platform = original_platform
            
            duration = time.time() - start_time
            
            if wait_time >= max_wait_time:
                return {
                    'success': False,
                    'error_message': '单账号查询超时',
                    'data': {'account': account, 'duration': duration}
                }
            
            self.logger(f"✅ {platform} 平台账号 {account} 数据查询完成")
            
            return {
                'success': True,
                'error_message': None,
                'data': {
                    'platform': platform,
                    'account': account,
                    'duration': duration
                }
            }
            
        except Exception as e:
            self.logger(f"❌ 查询 {platform} 平台账号 {account} 数据异常: {str(e)}")
            return {
                'success': False,
                'error_message': f'单账号查询异常: {str(e)}',
                'data': {'account': account}
            }
    
    def execute_draft_upload(self, platform: str, target_accounts: List[str]) -> Dict[str, Any]:
        """
        执行存稿任务
        
        Args:
            platform: 平台名称
            target_accounts: 目标账号列表
            
        Returns:
            执行结果
        """
        if not self.draft_task_manager:
            return {
                'success': False,
                'error_message': '存稿任务管理器未初始化',
                'data': None
            }
        
        if platform not in self.supported_platforms:
            return {
                'success': False,
                'error_message': f'不支持的平台: {platform}',
                'data': None
            }
        
        try:
            self.logger(f"📝 开始执行 {platform} 平台存稿任务")
            
            # 切换到指定平台
            original_platform = getattr(self.draft_task_manager.parent_ui, 'current_platform', 'netease')
            self.draft_task_manager.parent_ui.current_platform = platform
            
            # 记录开始时间
            start_time = time.time()
            
            # 根据目标账号执行不同的存稿策略
            if target_accounts == ["all"] or not target_accounts:
                # 为避免依赖 UI 当前平台的账号缓存，显式获取目标平台账号列表
                accounts = self.get_platform_accounts(platform)
                if not accounts:
                    self.logger(f"❌ 未找到 {platform} 平台的账号列表，无法执行存稿任务")
                    return {
                        'success': False,
                        'error_message': '未找到目标平台账号列表',
                        'data': {'target_accounts': target_accounts}
                    }
                # 使用并发模式执行全部账号（更贴合调度与可控并发）
                success = self.draft_task_manager.start_concurrent_task(accounts)
            else:
                # 执行指定账号的存稿任务
                if len(target_accounts) == 1:
                    success = self.draft_task_manager.start_task(target_accounts[0])
                else:
                    # 多个指定账号，使用并发模式
                    success = self.draft_task_manager.start_concurrent_task(target_accounts)
            
            if not success:
                return {
                    'success': False,
                    'error_message': '存稿任务启动失败',
                    'data': {'target_accounts': target_accounts}
                }
            
            # 等待存稿任务完成
            max_wait_time = 1800  # 存稿任务最大等待30分钟
            wait_time = 0
            check_interval = 5
            
            while self.draft_task_manager.is_running and wait_time < max_wait_time:
                time.sleep(check_interval)
                wait_time += check_interval
            
            # 恢复原平台
            self.draft_task_manager.parent_ui.current_platform = original_platform
            
            duration = time.time() - start_time
            
            if wait_time >= max_wait_time:
                return {
                    'success': False,
                    'error_message': '存稿任务执行超时',
                    'data': {'target_accounts': target_accounts, 'duration': duration}
                }
            
            # 获取执行结果
            success_count = getattr(self.draft_task_manager.parent_ui, 'success_count', 0)
            failure_count = getattr(self.draft_task_manager.parent_ui, 'failure_count', 0)
            
            self.logger(f"✅ {platform} 平台存稿任务完成: 成功 {success_count}, 失败 {failure_count}")
            
            return {
                'success': True,
                'error_message': None,
                'data': {
                    'platform': platform,
                    'target_accounts': target_accounts,
                    'upload_count': success_count,
                    'success_count': success_count,
                    'failure_count': failure_count,
                    'duration': duration
                }
            }
            
        except Exception as e:
            self.logger(f"❌ 执行 {platform} 平台存稿任务异常: {str(e)}")
            return {
                'success': False,
                'error_message': f'存稿任务异常: {str(e)}',
                'data': {'target_accounts': target_accounts}
            }
    
    def get_platform_accounts(self, platform: str) -> List[str]:
        """
        获取指定平台的账号列表
        
        Args:
            platform: 平台名称
            
        Returns:
            账号列表
        """
        try:
            if not self.data_query_manager:
                return []
            
            # 切换到指定平台
            original_platform = getattr(self.data_query_manager.parent_ui, 'current_platform', 'netease')
            self.data_query_manager.parent_ui.current_platform = platform
            
            # 获取账号列表
            accounts = self.data_query_manager.account_manager.load_accounts()
            
            # 恢复原平台
            self.data_query_manager.parent_ui.current_platform = original_platform
            
            return list(accounts) if accounts else []
            
        except Exception as e:
            self.logger(f"❌ 获取 {platform} 平台账号列表异常: {str(e)}")
            return []
    
    def validate_platform_account(self, platform: str, account: str) -> bool:
        """
        验证平台账号是否存在
        
        Args:
            platform: 平台名称
            account: 账号名称
            
        Returns:
            账号是否存在
        """
        if account == "all":
            return True
        
        accounts = self.get_platform_accounts(platform)
        return account in accounts
    
    def get_supported_platforms(self) -> List[str]:
        """获取支持的平台列表"""
        return self.supported_platforms.copy()
    
    def is_platform_available(self, platform: str) -> bool:
        """
        检查平台是否可用
        
        Args:
            platform: 平台名称
            
        Returns:
            平台是否可用
        """
        if platform not in self.supported_platforms:
            return False
        
        # 检查对应的管理器是否可用
        if platform in ["netease", "toutiao", "dayu"]:
            return self.data_query_manager is not None
        
        return False
