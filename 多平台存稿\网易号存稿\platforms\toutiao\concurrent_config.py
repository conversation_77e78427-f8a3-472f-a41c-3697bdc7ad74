"""
头条号平台并发处理配置模块
提供头条号平台的默认配置和配置管理功能
"""

import os
import json
from typing import Dict, Any, Optional


class ToutiaoConcurrentConfig:
    """头条号并发处理配置类"""

    # 默认配置
    DEFAULT_CONFIG = {
        # 并发处理配置
        "max_concurrent_workers": 3,           # 最大并发工作线程数
        "concurrent_batch_size": 5,            # 并发批处理大小
        "worker_timeout": 300,                 # 工作线程超时时间（秒）
        "retry_attempts": 3,                   # 重试次数
        "retry_delay": 5,                      # 重试延迟（秒）
        
        # 存稿处理配置
        "draft_limit": 10,                     # 每个账号存稿数量限制
        "loop_limit": 3,                       # 循环次数限制
        "archive_completed": True,             # 是否归档已完成视频
        "random_video_allocation": True,       # 是否随机分配视频
        
        # 浏览器配置
        "headless_mode": False,                # 是否使用无头模式
        "browser_timeout": 30,                 # 浏览器操作超时时间（秒）
        "page_load_timeout": 60,               # 页面加载超时时间（秒）
        
        # 上传配置
        "upload_timeout": 300,                 # 视频上传超时时间（秒）
        "upload_retry_attempts": 3,            # 上传重试次数
        "upload_chunk_size": 1024 * 1024,      # 上传块大小（字节）
        
        # 日志配置
        "log_level": "INFO",                   # 日志级别
        "log_to_file": True,                   # 是否记录到文件
        "log_file_max_size": 10 * 1024 * 1024, # 日志文件最大大小（字节）
        
        # 安全配置
        "request_delay_min": 2,                # 请求最小延迟（秒）
        "request_delay_max": 5,                # 请求最大延迟（秒）
        "concurrent_login_delay": 10,          # 并发登录延迟（秒）
        
        # 错误处理配置
        "ignore_minor_errors": True,           # 是否忽略轻微错误
        "stop_on_critical_error": True,        # 是否在关键错误时停止
        "error_screenshot": True,              # 是否在错误时截图
        
        # 性能优化配置
        "enable_gpu_acceleration": False,      # 是否启用GPU加速
        "memory_limit_mb": 2048,               # 内存限制（MB）
        "disk_cache_size_mb": 512,             # 磁盘缓存大小（MB）
    }

    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器

        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self.DEFAULT_CONFIG.copy()
        
        # 如果指定了配置文件，尝试加载
        if config_file and os.path.exists(config_file):
            self.load_config()

    def load_config(self) -> bool:
        """
        从文件加载配置

        Returns:
            是否成功加载
        """
        if not self.config_file or not os.path.exists(self.config_file):
            return False

        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
                
                # 合并配置，文件配置覆盖默认配置
                self.config.update(file_config)
                
            return True

        except Exception as e:
            print(f"加载头条号并发配置失败: {str(e)}")
            return False

    def save_config(self) -> bool:
        """
        保存配置到文件

        Returns:
            是否成功保存
        """
        if not self.config_file:
            return False

        try:
            # 确保配置文件目录存在
            config_dir = os.path.dirname(self.config_file)
            if config_dir and not os.path.exists(config_dir):
                os.makedirs(config_dir)

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
                
            return True

        except Exception as e:
            print(f"保存头条号并发配置失败: {str(e)}")
            return False

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值

        Args:
            key: 配置键
            default: 默认值

        Returns:
            配置值
        """
        return self.config.get(key, default)

    def set(self, key: str, value: Any) -> None:
        """
        设置配置值

        Args:
            key: 配置键
            value: 配置值
        """
        self.config[key] = value

    def get_concurrent_config(self) -> Dict[str, Any]:
        """
        获取并发处理相关配置

        Returns:
            并发配置字典
        """
        return {
            "max_workers": self.get("max_concurrent_workers"),
            "batch_size": self.get("concurrent_batch_size"),
            "timeout": self.get("worker_timeout"),
            "retry_attempts": self.get("retry_attempts"),
            "retry_delay": self.get("retry_delay"),
            "login_delay": self.get("concurrent_login_delay")
        }

    def get_processing_config(self) -> Dict[str, Any]:
        """
        获取处理相关配置

        Returns:
            处理配置字典
        """
        return {
            "draft_limit": self.get("draft_limit"),
            "loop_limit": self.get("loop_limit"),
            "archive_completed": self.get("archive_completed"),
            "random_video_allocation": self.get("random_video_allocation"),
            "headless_mode": self.get("headless_mode")
        }

    def get_upload_config(self) -> Dict[str, Any]:
        """
        获取上传相关配置

        Returns:
            上传配置字典
        """
        return {
            "timeout": self.get("upload_timeout"),
            "retry_attempts": self.get("upload_retry_attempts"),
            "chunk_size": self.get("upload_chunk_size")
        }

    def get_security_config(self) -> Dict[str, Any]:
        """
        获取安全相关配置

        Returns:
            安全配置字典
        """
        return {
            "request_delay_min": self.get("request_delay_min"),
            "request_delay_max": self.get("request_delay_max"),
            "ignore_minor_errors": self.get("ignore_minor_errors"),
            "stop_on_critical_error": self.get("stop_on_critical_error"),
            "error_screenshot": self.get("error_screenshot")
        }

    def validate_config(self) -> list:
        """
        验证配置的有效性

        Returns:
            错误信息列表，空列表表示配置有效
        """
        errors = []

        # 验证数值配置
        numeric_configs = [
            ("max_concurrent_workers", 1, 20),
            ("concurrent_batch_size", 1, 50),
            ("worker_timeout", 30, 3600),
            ("retry_attempts", 0, 10),
            ("retry_delay", 1, 60),
            ("draft_limit", 0, 1000),
            ("loop_limit", 0, 100),
            ("upload_timeout", 60, 3600),
            ("upload_retry_attempts", 1, 10),
            ("request_delay_min", 0, 30),
            ("request_delay_max", 1, 60)
        ]

        for key, min_val, max_val in numeric_configs:
            value = self.get(key)
            if not isinstance(value, (int, float)) or value < min_val or value > max_val:
                errors.append(f"配置 {key} 的值 {value} 不在有效范围 [{min_val}, {max_val}] 内")

        # 验证布尔配置
        boolean_configs = [
            "archive_completed", "random_video_allocation", "headless_mode",
            "log_to_file", "ignore_minor_errors", "stop_on_critical_error",
            "error_screenshot", "enable_gpu_acceleration"
        ]

        for key in boolean_configs:
            value = self.get(key)
            if not isinstance(value, bool):
                errors.append(f"配置 {key} 的值 {value} 不是布尔类型")

        # 验证逻辑关系
        if self.get("request_delay_min") >= self.get("request_delay_max"):
            errors.append("request_delay_min 必须小于 request_delay_max")

        return errors

    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self.config = self.DEFAULT_CONFIG.copy()

    def get_all_config(self) -> Dict[str, Any]:
        """
        获取所有配置

        Returns:
            完整配置字典
        """
        return self.config.copy()


# 全局配置实例
_global_config = None


def get_toutiao_config(config_file: Optional[str] = None) -> ToutiaoConcurrentConfig:
    """
    获取头条号配置实例（单例模式）

    Args:
        config_file: 配置文件路径

    Returns:
        配置实例
    """
    global _global_config
    
    if _global_config is None:
        _global_config = ToutiaoConcurrentConfig(config_file)
    
    return _global_config


def create_default_config_file(file_path: str) -> bool:
    """
    创建默认配置文件

    Args:
        file_path: 配置文件路径

    Returns:
        是否成功创建
    """
    try:
        config = ToutiaoConcurrentConfig()
        config.config_file = file_path
        return config.save_config()
    except Exception as e:
        print(f"创建默认头条号配置文件失败: {str(e)}")
        return False
