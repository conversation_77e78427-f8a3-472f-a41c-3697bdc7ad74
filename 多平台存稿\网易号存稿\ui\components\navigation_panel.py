"""
左侧导航面板组件 - 遵循MECE原则的独立UI组件

职责：
- 创建和管理左侧导航面板
- 显示统计信息
- 管理平台导航
- 提供滚动功能

独立性：不依赖其他UI组件，只依赖配置管理器和平台侧边栏
"""

import tkinter as tk
from tkinter import ttk
from typing import Optional, Callable


class NavigationPanel:
    """左侧导航面板组件"""
    
    def __init__(self, parent: tk.Widget, config_manager, platform_sidebar_class=None):
        """
        初始化导航面板
        
        Args:
            parent: 父容器
            config_manager: 配置管理器
            platform_sidebar_class: 平台侧边栏类
        """
        self.parent = parent
        self.config_manager = config_manager
        self.platform_sidebar_class = platform_sidebar_class
        
        # 创建主容器
        self.container = ttk.Frame(parent)
        
        # 统计信息标签引用
        self.current_platform_label = None
        self.current_account_label = None
        self.account_count_label = None
        self.running_status_label = None
        self.task_progress_label = None

        # 新增状态显示标签引用
        self.success_fail_stats_label = None
        self.runtime_label = None

        
        # 平台侧边栏引用
        self.platform_sidebar = None
        
        # 创建组件
        self._create_navigation_panel()
    
    def _create_navigation_panel(self):
        """创建左侧导航面板 - 带滚动条"""
        # 创建主容器 - 最小边距，让侧边栏尽可能窄
        nav_container = ttk.Frame(self.container)
        nav_container.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)  # 最小边距
        
        # 创建滚动区域
        self._create_scrollable_content(nav_container)
    
    def _create_scrollable_content(self, parent):
        """创建可滚动的内容区域 - 删除滚动条，只保留鼠标滚轮滚动"""
        # 创建Canvas（不再创建Scrollbar）
        canvas = tk.Canvas(parent, highlightthickness=0)
        scrollable_frame = ttk.Frame(canvas)

        # 配置滚动
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        # 创建窗口
        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

        # 布局Canvas（不再布局Scrollbar）
        canvas.pack(fill="both", expand=True)

        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            try:
                canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            except:
                pass  # 忽略滚动错误

        def _bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind('<Enter>', _bind_mousewheel)
        canvas.bind('<Leave>', _unbind_mousewheel)

        # 配置Canvas窗口大小自适应
        def _configure_canvas_window(event):
            # 更新scrollable_frame的宽度以匹配canvas
            canvas_width = event.width
            canvas.itemconfig(canvas_window, width=canvas_width)

        canvas.bind('<Configure>', _configure_canvas_window)

        # 在scrollable_frame中创建内容
        self._create_navigation_content(scrollable_frame)
    
    def _create_navigation_content(self, parent):
        """创建导航面板的内容"""
        # 统计信息面板 - 放在最上方
        self._create_stats_panel(parent)
        
        # 创建平台导航侧边栏
        self._create_platform_navigation(parent)
    
    def _create_stats_panel(self, parent):
        """创建统计信息面板"""
        # 使用LabelFrame创建卡片式设计 - 减少内边距让侧边栏更紧凑
        stats_card = ttk.LabelFrame(parent, text="📊 统计信息", padding=5)  # 从10减少到5
        stats_card.pack(fill=tk.X, pady=(0, 10))  # 从15减少到10
        
        # 当前平台信息
        platform_frame = ttk.Frame(stats_card)
        platform_frame.pack(fill=tk.X, pady=(0, 8))
        
        ttk.Label(
            platform_frame,
            text="当前平台:",
            font=("微软雅黑", 9),
            foreground="#666666"
        ).pack(side=tk.LEFT)
        
        self.current_platform_label = ttk.Label(
            platform_frame,
            text=self._get_platform_display_name(),
            font=("微软雅黑", 9, "bold"),
            foreground="#2E86AB"
        )
        self.current_platform_label.pack(side=tk.RIGHT)
        
        # 添加分隔线
        separator = ttk.Separator(stats_card, orient='horizontal')
        separator.pack(fill=tk.X, pady=(5, 8))
        
        # 当前账号信息
        account_frame = ttk.Frame(stats_card)
        account_frame.pack(fill=tk.X, pady=(0, 8))
        
        ttk.Label(
            account_frame,
            text="当前账号:",
            font=("微软雅黑", 9),
            foreground="#666666"
        ).pack(side=tk.LEFT)
        
        self.current_account_label = ttk.Label(
            account_frame,
            text="未选择",
            font=("微软雅黑", 9),
            foreground="#666666"
        )
        self.current_account_label.pack(side=tk.RIGHT)
        
        # 账号数量
        count_frame = ttk.Frame(stats_card)
        count_frame.pack(fill=tk.X, pady=(0, 8))
        
        ttk.Label(
            count_frame,
            text="账号数量:",
            font=("微软雅黑", 9),
            foreground="#666666"
        ).pack(side=tk.LEFT)
        
        self.account_count_label = ttk.Label(
            count_frame,
            text="0",
            font=("微软雅黑", 9, "bold"),
            foreground="#28A745"
        )
        self.account_count_label.pack(side=tk.RIGHT)
        
        # 运行状态
        status_frame = ttk.Frame(stats_card)
        status_frame.pack(fill=tk.X, pady=(0, 8))
        
        ttk.Label(
            status_frame,
            text="运行状态:",
            font=("微软雅黑", 9),
            foreground="#666666"
        ).pack(side=tk.LEFT)
        
        self.running_status_label = ttk.Label(
            status_frame,
            text="🟢 空闲",
            font=("微软雅黑", 9),
            foreground="#28A745"
        )
        self.running_status_label.pack(side=tk.RIGHT)
        
        # 任务进度
        progress_frame = ttk.Frame(stats_card)
        progress_frame.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(
            progress_frame,
            text="任务进度:",
            font=("微软雅黑", 9),
            foreground="#666666"
        ).pack(side=tk.LEFT)

        self.task_progress_label = ttk.Label(
            progress_frame,
            text="0/0",
            font=("微软雅黑", 9),
            foreground="#666666"
        )
        self.task_progress_label.pack(side=tk.RIGHT)

        # 添加分隔线
        separator2 = ttk.Separator(stats_card, orient='horizontal')
        separator2.pack(fill=tk.X, pady=(5, 8))

        # 成功/失败统计
        stats_frame = ttk.Frame(stats_card)
        stats_frame.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(
            stats_frame,
            text="统计:",
            font=("微软雅黑", 9),
            foreground="#666666"
        ).pack(side=tk.LEFT)

        self.success_fail_stats_label = ttk.Label(
            stats_frame,
            text="成功: 0, 失败: 0",
            font=("微软雅黑", 9),
            foreground="#666666"
        )
        self.success_fail_stats_label.pack(side=tk.RIGHT)

        # 运行时长
        runtime_frame = ttk.Frame(stats_card)
        runtime_frame.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(
            runtime_frame,
            text="运行时长:",
            font=("微软雅黑", 9),
            foreground="#666666"
        ).pack(side=tk.LEFT)

        self.runtime_label = ttk.Label(
            runtime_frame,
            text="00:00:00",
            font=("微软雅黑", 9),
            foreground="#666666"
        )
        self.runtime_label.pack(side=tk.RIGHT)
        

    
    def _create_platform_navigation(self, parent):
        """创建平台导航区域"""
        if self.platform_sidebar_class:
            try:
                # 导入平台侧边栏类
                from 网易号存稿.ui.components.platform_sidebar import PlatformSidebar
                
                # 创建平台侧边栏
                self.platform_sidebar = PlatformSidebar(
                    parent,
                    self.config_manager,
                    log_callback=None,  # 稍后设置
                    on_platform_change=None  # 稍后设置
                )
                
                # 设置主程序UI引用
                # 注意：这里需要在主程序中设置引用
                
            except ImportError:
                # 如果导入失败，创建一个简单的占位符
                placeholder = ttk.Label(parent, text="平台导航加载中...")
                placeholder.pack(pady=10)
    
    def _get_platform_display_name(self) -> str:
        """获取平台显示名称"""
        current_platform = self.config_manager.get_current_platform()
        platform_names = self.config_manager.get("platform_names", {})
        return platform_names.get(current_platform, current_platform)
    
    def update_stats_panel(self):
        """更新统计信息面板（保留但不再作为平台名称的主数据链）"""
        try:
            # 其他统计信息可在此更新；平台名称由主UI显式下发，避免多处数据源不一致
            pass
        except RuntimeError:
            return
        except Exception:
            return

    def set_platform_display_name(self, platform_name: str):
        """由主UI显式设置平台显示名称（与顶部右上角一致的数据链）"""
        try:
            if self.current_platform_label:
                self.current_platform_label.config(text=platform_name)
        except RuntimeError:
            return
        except Exception:
            return

    def update_current_account(self, account_name):
        """更新当前账号显示"""
        if self.current_account_label:
            try:
                # 确保account_name是字符串
                account_str = str(account_name) if account_name is not None else "未选择"

                if account_str and account_str != "未选择":
                    # 提取账号名称（去掉状态标识）
                    clean_name = account_str.split('(')[0].strip() if '(' in account_str else account_str
                    self.current_account_label.config(text=clean_name, foreground="#2E86AB")
                else:
                    self.current_account_label.config(text="未选择", foreground="#666666")
            except RuntimeError:
                return  # 主线程已结束，静默忽略
    
    def update_account_count(self, count: int):
        """更新账号数量显示"""
        if self.account_count_label:
            try:
                self.account_count_label.config(text=str(count))
            except RuntimeError:
                return  # 主线程已结束，静默忽略
    
    def update_running_status(self, status: str, color: str = "#28A745"):
        """更新运行状态显示"""
        if self.running_status_label:
            try:
                self.running_status_label.config(text=status, foreground=color)
            except RuntimeError:
                return  # 主线程已结束，静默忽略

    def update_task_progress(self, progress: str, color: str = "#007BFF"):
        """更新任务进度显示"""
        if self.task_progress_label:
            try:
                self.task_progress_label.config(text=progress, foreground=color)
            except RuntimeError:
                return  # 主线程已结束，静默忽略

    def update_success_fail_stats(self, success_count: int, fail_count: int):
        """更新成功/失败统计显示"""
        if self.success_fail_stats_label:
            try:
                # 创建带颜色的文本显示
                stats_text = f"成功: {success_count}, 失败: {fail_count}"

                # 根据成功失败情况选择颜色
                if fail_count == 0 and success_count > 0:
                    color = "#28A745"  # 全部成功 - 绿色
                elif success_count == 0 and fail_count > 0:
                    color = "#DC3545"  # 全部失败 - 红色
                elif success_count > 0 and fail_count > 0:
                    color = "#FFC107"  # 部分成功 - 黄色
                else:
                    color = "#666666"  # 默认灰色

                self.success_fail_stats_label.config(text=stats_text, foreground=color)
            except RuntimeError:
                return  # 主线程已结束，静默忽略

    def reset_success_fail_stats(self):
        """重置成功/失败统计显示"""
        if self.success_fail_stats_label:
            try:
                self.success_fail_stats_label.config(text="成功: 0, 失败: 0", foreground="#666666")
            except RuntimeError:
                return  # 主线程已结束，静默忽略

    def update_runtime(self, runtime_text: str):
        """更新运行时长显示"""
        if self.runtime_label:
            try:
                self.runtime_label.config(text=runtime_text, foreground="#007BFF")
            except RuntimeError:
                return  # 主线程已结束，静默忽略

    def reset_runtime(self):
        """重置运行时长显示"""
        if self.runtime_label:
            try:
                self.runtime_label.config(text="00:00:00", foreground="#666666")
            except RuntimeError:
                return  # 主线程已结束，静默忽略
    
    def set_platform_sidebar_ui_reference(self, ui_reference):
        """设置平台侧边栏的UI引用"""
        if self.platform_sidebar:
            # 设置日志回调
            self.platform_sidebar.log_callback = getattr(ui_reference, 'log', None)
            # 设置平台切换回调
            self.platform_sidebar.on_platform_change = getattr(ui_reference, 'switch_platform', None)
            # 如果有set_ui_reference方法，也调用它
            if hasattr(self.platform_sidebar, 'set_ui_reference'):
                self.platform_sidebar.set_ui_reference(ui_reference)
    
    def pack(self, **kwargs):
        """打包容器"""
        self.container.pack(**kwargs)
    
    def grid(self, **kwargs):
        """网格布局容器"""
        self.container.grid(**kwargs)
    
    def destroy(self):
        """销毁组件"""
        if hasattr(self, 'container'):
            self.container.destroy()
