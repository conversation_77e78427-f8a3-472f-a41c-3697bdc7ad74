"""
升级版定时任务调度管理器
支持用户自主任务管理、一次性任务和常驻任务
"""

import threading
import time
import datetime
from typing import Dict, List, Optional, Callable, Any
from concurrent.futures import ThreadPoolExecutor

from .task_model import ScheduledTask, TaskType, TaskStatus, RepeatRule, RepeatType
from .task_storage import TaskStorage
from .task_executor import TaskExecutor
from .platform_integration import PlatformIntegration
from .time_utils import TimeUtils
from .task_monitor import TaskMonitor


class AdvancedSchedulerManager:
    """升级版定时任务调度管理器"""
    
    def __init__(self, 
                 storage_dir: str = None,
                 max_concurrent_tasks: int = 3,
                 check_interval: int = 1,
                 logger: Optional[Callable] = None):
        """
        初始化调度管理器
        
        Args:
            storage_dir: 存储目录
            max_concurrent_tasks: 最大并发任务数
            check_interval: 检查间隔（秒）
            logger: 日志记录函数
        """
        self.logger = logger or print
        self.check_interval = check_interval
        
        # 初始化组件
        self.storage = TaskStorage(storage_dir)
        self.executor = TaskExecutor(max_concurrent_tasks, self.logger)
        self.platform_integration = PlatformIntegration(logger=self.logger)
        self.monitor = TaskMonitor(logger=self.logger)
        
        # 设置平台集成器和监控器
        self.executor.set_platform_integration(self.platform_integration)
        self.executor.set_monitor(self.monitor)
        
        # 任务管理
        self.tasks: Dict[str, ScheduledTask] = {}
        self.running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        
        # 统计信息
        self.total_executions = 0
        self.successful_executions = 0
        self.failed_executions = 0
        
        # 加载配置和任务
        self.config = self.storage.load_config()
        self._load_tasks()
        
        self.logger("✅ 定时任务调度管理器初始化完成")

    def set_managers(self, data_query_manager=None, draft_task_manager=None):
        """设置外部管理器"""
        self.platform_integration.set_managers(data_query_manager, draft_task_manager)
    
    def _load_tasks(self):
        """从存储加载任务"""
        try:
            tasks = self.storage.load_tasks()
            self.tasks = {task.id: task for task in tasks}
            self.logger(f"📋 加载了 {len(self.tasks)} 个任务")
        except Exception as e:
            self.logger(f"❌ 加载任务失败: {e}")
            self.tasks = {}
    
    def start_scheduler(self):
        """启动调度器"""
        if self.running:
            self.logger("⚠️ 调度器已在运行中")
            return
        
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        self.logger("🚀 定时任务调度器已启动")
    
    def stop_scheduler(self):
        """停止调度器"""
        if not self.running:
            return
        
        self.running = False
        
        # 等待调度器线程结束
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        # 关闭任务执行器
        self.executor.shutdown(wait=False)
        
        self.logger("⏹️ 定时任务调度器已停止")
    
    def _scheduler_loop(self):
        """调度器主循环"""
        self.logger("🔄 调度器主循环开始")
        
        while self.running:
            try:
                current_time = TimeUtils.get_beijing_now()
                
                # 检查需要执行的任务
                tasks_to_execute = []
                for task in self.tasks.values():
                    if task.should_execute(current_time):
                        tasks_to_execute.append(task)
                
                # 执行任务
                for task in tasks_to_execute:
                    self._execute_task(task)
                
                # 清理已完成的一次性任务
                self._cleanup_completed_tasks()
                
                # 等待下一个检查周期
                time.sleep(self.check_interval)
                
            except Exception as e:
                self.logger(f"❌ 调度器循环异常: {e}")
                time.sleep(self.check_interval)
        
        self.logger("🔄 调度器主循环结束")
    
    def _execute_task(self, task: ScheduledTask):
        """执行任务"""
        try:
            self.logger(f"⏰ 准备执行任务: {task.name}")

            # 记录任务开始执行
            self.monitor.task_started(task)

            # 提交任务到执行器
            success = self.executor.execute_task(task)

            if success:
                self.total_executions += 1
                # 保存任务状态
                self._save_task(task)
            else:
                self.logger(f"❌ 任务提交失败: {task.name}")
                # 记录任务失败
                self.monitor.task_completed(task, False, 0, "任务提交失败")

        except Exception as e:
            self.logger(f"❌ 执行任务异常 {task.name}: {e}")
            # 记录任务异常
            self.monitor.task_completed(task, False, 0, f"执行异常: {str(e)}")
    
    def _cleanup_completed_tasks(self):
        """清理已完成的一次性任务"""
        try:
            completed_tasks = []
            for task_id, task in self.tasks.items():
                if (task.repeat_rule.type == RepeatType.ONCE and 
                    task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED] and
                    not task.enabled):
                    completed_tasks.append(task_id)
            
            # 移除已完成的一次性任务
            for task_id in completed_tasks:
                self.tasks.pop(task_id, None)
            
            if completed_tasks:
                self.logger(f"🧹 清理了 {len(completed_tasks)} 个已完成的一次性任务")
                self._save_all_tasks()
                
        except Exception as e:
            self.logger(f"❌ 清理任务异常: {e}")
    
    def create_task(self, 
                   name: str,
                   task_type: TaskType,
                   schedule_time: datetime.datetime,
                   repeat_rule: Optional[RepeatRule] = None,
                   description: str = "",
                   platform: str = "netease",
                   target_accounts: Optional[List[str]] = None) -> str:
        """
        创建新任务
        
        Args:
            name: 任务名称
            task_type: 任务类型
            schedule_time: 执行时间
            repeat_rule: 重复规则
            description: 任务描述
            platform: 目标平台
            target_accounts: 目标账号列表
            
        Returns:
            任务ID
        """
        try:
            # 验证参数
            if not name.strip():
                raise ValueError("任务名称不能为空")
            
            if not isinstance(task_type, TaskType):
                raise ValueError("无效的任务类型")
            
            # 验证时间
            valid, error_msg = TimeUtils.validate_time_range(schedule_time)
            if not valid:
                raise ValueError(error_msg)
            
            # 验证平台和账号
            if not self.platform_integration.is_platform_available(platform):
                raise ValueError(f"平台 {platform} 不可用")
            
            if target_accounts:
                for account in target_accounts:
                    if account != "all" and not self.platform_integration.validate_platform_account(platform, account):
                        raise ValueError(f"账号 {account} 在平台 {platform} 中不存在")
            
            # 创建任务
            task = ScheduledTask(
                name=name,
                task_type=task_type,
                schedule_time=schedule_time,
                repeat_rule=repeat_rule or RepeatRule(),
                description=description,
                platform=platform,
                target_accounts=target_accounts or ["all"]
            )
            
            # 添加到任务列表
            self.tasks[task.id] = task
            
            # 保存任务
            self._save_task(task)
            
            self.logger(f"✅ 创建任务成功: {name} (ID: {task.id})")
            return task.id
            
        except Exception as e:
            self.logger(f"❌ 创建任务失败: {e}")
            raise

    def update_task(self, task_id: str, **kwargs) -> bool:
        """
        更新任务

        Args:
            task_id: 任务ID
            **kwargs: 要更新的字段

        Returns:
            是否更新成功
        """
        try:
            if task_id not in self.tasks:
                raise ValueError(f"任务不存在: {task_id}")

            task = self.tasks[task_id]

            # 更新字段
            if 'name' in kwargs:
                if not kwargs['name'].strip():
                    raise ValueError("任务名称不能为空")
                task.name = kwargs['name']

            if 'description' in kwargs:
                task.description = kwargs['description']

            if 'schedule_time' in kwargs:
                valid, error_msg = TimeUtils.validate_time_range(kwargs['schedule_time'])
                if not valid:
                    raise ValueError(error_msg)
                task.schedule_time = kwargs['schedule_time']
                task.next_execution = kwargs['schedule_time']

            if 'repeat_rule' in kwargs:
                task.repeat_rule = kwargs['repeat_rule']

            if 'platform' in kwargs:
                if not self.platform_integration.is_platform_available(kwargs['platform']):
                    raise ValueError(f"平台 {kwargs['platform']} 不可用")
                task.platform = kwargs['platform']

            if 'target_accounts' in kwargs:
                task.target_accounts = kwargs['target_accounts']

            if 'enabled' in kwargs:
                task.enabled = kwargs['enabled']

            # 更新时间戳
            task.updated_at = TimeUtils.get_beijing_now()

            # 保存任务
            self._save_task(task)

            self.logger(f"✅ 更新任务成功: {task.name}")
            return True

        except Exception as e:
            self.logger(f"❌ 更新任务失败: {e}")
            return False

    def delete_task(self, task_id: str) -> bool:
        """
        删除任务

        Args:
            task_id: 任务ID

        Returns:
            是否删除成功
        """
        try:
            if task_id not in self.tasks:
                return False

            task = self.tasks[task_id]

            # 如果任务正在运行，先取消
            self.executor.cancel_task(task_id)

            # 从内存中移除
            del self.tasks[task_id]

            # 从存储中删除
            self.storage.delete_task(task_id)

            self.logger(f"✅ 删除任务成功: {task.name}")
            return True

        except Exception as e:
            self.logger(f"❌ 删除任务失败: {e}")
            return False

    def get_task(self, task_id: str) -> Optional[ScheduledTask]:
        """
        获取任务

        Args:
            task_id: 任务ID

        Returns:
            任务对象，如果不存在返回None
        """
        return self.tasks.get(task_id)

    def get_all_tasks(self) -> List[ScheduledTask]:
        """
        获取所有任务

        Returns:
            任务列表
        """
        return list(self.tasks.values())

    def enable_task(self, task_id: str) -> bool:
        """
        启用任务

        Args:
            task_id: 任务ID

        Returns:
            是否成功
        """
        return self.update_task(task_id, enabled=True)

    def disable_task(self, task_id: str) -> bool:
        """
        禁用任务

        Args:
            task_id: 任务ID

        Returns:
            是否成功
        """
        return self.update_task(task_id, enabled=False)

    def execute_task_now(self, task_id: str) -> bool:
        """
        立即执行任务

        Args:
            task_id: 任务ID

        Returns:
            是否成功提交执行
        """
        try:
            if task_id not in self.tasks:
                self.logger(f"❌ 任务不存在: {task_id}")
                return False

            task = self.tasks[task_id]

            if not task.enabled:
                self.logger(f"❌ 任务已禁用: {task.name}")
                return False

            # 立即执行任务
            success = self.executor.execute_task(task)

            if success:
                self.logger(f"✅ 任务已提交立即执行: {task.name}")
            else:
                self.logger(f"❌ 任务提交失败: {task.name}")

            return success

        except Exception as e:
            self.logger(f"❌ 立即执行任务失败: {e}")
            return False

    def get_next_execution_time(self, task_id: str) -> Optional[datetime.datetime]:
        """
        获取任务的下次执行时间

        Args:
            task_id: 任务ID

        Returns:
            下次执行时间，如果不存在返回None
        """
        task = self.get_task(task_id)
        return task.next_execution if task else None

    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """
        获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            任务状态，如果不存在返回None
        """
        task = self.get_task(task_id)
        return task.status if task else None

    def get_running_tasks(self) -> List[ScheduledTask]:
        """
        获取正在运行的任务列表

        Returns:
            正在运行的任务列表
        """
        return [task for task in self.tasks.values() if task.status == TaskStatus.RUNNING]

    def get_pending_tasks(self) -> List[ScheduledTask]:
        """
        获取等待执行的任务列表

        Returns:
            等待执行的任务列表
        """
        return [task for task in self.tasks.values()
                if task.status == TaskStatus.PENDING and task.enabled]

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取调度器统计信息

        Returns:
            统计信息字典
        """
        total_tasks = len(self.tasks)
        enabled_tasks = len([t for t in self.tasks.values() if t.enabled])
        running_tasks = len(self.get_running_tasks())
        pending_tasks = len(self.get_pending_tasks())

        return {
            'total_tasks': total_tasks,
            'enabled_tasks': enabled_tasks,
            'disabled_tasks': total_tasks - enabled_tasks,
            'running_tasks': running_tasks,
            'pending_tasks': pending_tasks,
            'total_executions': self.total_executions,
            'successful_executions': self.successful_executions,
            'failed_executions': self.failed_executions,
            'scheduler_running': self.running
        }

    def _save_task(self, task: ScheduledTask):
        """保存单个任务"""
        try:
            self.storage.save_task(task)
        except Exception as e:
            self.logger(f"❌ 保存任务失败 {task.name}: {e}")

    def _save_all_tasks(self):
        """保存所有任务"""
        try:
            tasks = list(self.tasks.values())
            self.storage.save_tasks(tasks)
        except Exception as e:
            self.logger(f"❌ 保存所有任务失败: {e}")

    def export_tasks(self, export_file: str) -> bool:
        """
        导出任务到文件

        Args:
            export_file: 导出文件路径

        Returns:
            是否导出成功
        """
        try:
            return self.storage.export_tasks(export_file)
        except Exception as e:
            self.logger(f"❌ 导出任务失败: {e}")
            return False

    def import_tasks(self, import_file: str, merge: bool = True) -> int:
        """
        从文件导入任务

        Args:
            import_file: 导入文件路径
            merge: 是否与现有任务合并

        Returns:
            导入的任务数量
        """
        try:
            count = self.storage.import_tasks(import_file, merge)

            # 重新加载任务
            self._load_tasks()

            self.logger(f"✅ 导入了 {count} 个任务")
            return count
        except Exception as e:
            self.logger(f"❌ 导入任务失败: {e}")
            return 0

    def cleanup_completed_tasks(self, days: int = None) -> int:
        """
        清理已完成的任务

        Args:
            days: 清理多少天前的任务

        Returns:
            清理的任务数量
        """
        try:
            count = self.storage.cleanup_completed_tasks(days)

            # 重新加载任务
            self._load_tasks()

            return count
        except Exception as e:
            self.logger(f"❌ 清理任务失败: {e}")
            return 0

    def get_supported_platforms(self) -> List[str]:
        """获取支持的平台列表"""
        return self.platform_integration.get_supported_platforms()

    def get_platform_accounts(self, platform: str) -> List[str]:
        """获取指定平台的账号列表"""
        return self.platform_integration.get_platform_accounts(platform)

    def validate_task_config(self,
                           name: str,
                           task_type: TaskType,
                           schedule_time: datetime.datetime,
                           platform: str,
                           target_accounts: List[str]):
        """
        验证任务配置

        Args:
            name: 任务名称
            task_type: 任务类型
            schedule_time: 执行时间
            platform: 平台
            target_accounts: 目标账号

        Returns:
            (是否有效, 错误信息)
        """
        try:
            # 验证名称
            if not name.strip():
                return False, "任务名称不能为空"

            # 验证任务类型
            if not isinstance(task_type, TaskType):
                return False, "无效的任务类型"

            # 验证时间
            valid, error_msg = TimeUtils.validate_time_range(schedule_time)
            if not valid:
                return False, error_msg

            # 验证平台
            if not self.platform_integration.is_platform_available(platform):
                return False, f"平台 {platform} 不可用"

            # 验证账号
            if target_accounts:
                for account in target_accounts:
                    if account != "all" and not self.platform_integration.validate_platform_account(platform, account):
                        return False, f"账号 {account} 在平台 {platform} 中不存在"

            return True, ""

        except Exception as e:
            return False, f"验证异常: {str(e)}"

    def get_task_logs(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取任务执行日志"""
        return self.monitor.get_recent_logs(limit)

    def get_task_performance(self, task_id: str = None) -> Dict[str, Any]:
        """获取任务性能指标"""
        return self.monitor.get_task_performance(task_id)

    def get_daily_statistics(self, days: int = 7) -> Dict[str, Dict[str, int]]:
        """获取每日统计数据"""
        return self.monitor.get_daily_statistics(days)

    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        return self.monitor.get_system_health()

    def cleanup_old_logs(self, days: int = 30):
        """清理旧日志数据"""
        self.monitor.cleanup_old_data(days)

    def export_execution_logs(self, start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """导出执行日志"""
        return self.monitor.export_logs(start_date, end_date)
