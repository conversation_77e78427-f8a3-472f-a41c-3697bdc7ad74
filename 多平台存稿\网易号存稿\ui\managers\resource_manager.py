#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源管理器 - 负责资源清理和管理相关功能
遵循MECE原则：与资源管理相关的所有功能集中管理
"""

import threading
import time
import os
import glob
from typing import Dict, Any, Optional, List


class ResourceManager:
    """资源管理器 - 负责所有资源清理和管理相关功能"""
    
    def __init__(self, parent_ui, config_manager):
        """
        初始化资源管理器
        
        Args:
            parent_ui: 主UI实例
            config_manager: 配置管理器
        """
        self.parent_ui = parent_ui
        self.config_manager = config_manager
        
        # 清理状态
        self.cleanup_in_progress = False
        self.cleanup_threads = []
    
    def cleanup_all_resources(self):
        """清理所有资源"""
        if self.cleanup_in_progress:
            return
        
        self.cleanup_in_progress = True
        self.parent_ui.log("🧹 开始清理所有资源...")
        
        try:
            # 清理运行中的任务
            self.cleanup_running_tasks()
            
            # 清理定时器线程
            self.cleanup_timer_threads()
            
            # 清理浏览器进程
            self.cleanup_browser_processes()
            
            # 清理日志处理线程
            self.cleanup_log_handler()
            
            # 清理存稿处理器
            self.cleanup_draft_processors()
            
            # 清理线程池
            self.cleanup_thread_pools()
            
            # 强制停止工作线程
            self.force_stop_worker_threads()
            
            # 清理临时文件
            self.cleanup_temp_files()
            
            # 强制终止子进程
            self.force_terminate_subprocesses()
            
            self.parent_ui.log("✅ 资源清理完成")
            
        except Exception as e:
            print(f"清理资源时发生错误: {e}")
        finally:
            self.cleanup_in_progress = False
    
    def cleanup_running_tasks(self):
        """清理正在运行的任务"""
        try:
            # 设置停止标志
            if hasattr(self.parent_ui, 'is_running'):
                self.parent_ui.is_running = False
            
            # 停止存稿任务管理器
            if hasattr(self.parent_ui, 'draft_task_manager'):
                self.parent_ui.draft_task_manager.stop_task()
            
            # 停止数据查询管理器
            if hasattr(self.parent_ui, 'data_query_manager'):
                self.parent_ui.data_query_manager.stop_query()
            
            # 停止并发管理器 - 按照原始版本的方式
            if hasattr(self.parent_ui, 'concurrent_manager') and self.parent_ui.concurrent_manager:
                self.parent_ui.log("正在停止并发管理器...")
                if hasattr(self.parent_ui.concurrent_manager, 'stop'):
                    self.parent_ui.concurrent_manager.stop()
                if hasattr(self.parent_ui.concurrent_manager, 'shutdown'):
                    self.parent_ui.concurrent_manager.shutdown(wait=False)
            
            self.parent_ui.log("✅ 运行中的任务已停止")
            
        except Exception as e:
            print(f"清理运行任务失败: {e}")
    
    def cleanup_timer_threads(self):
        """清理定时器线程"""
        try:
            # 旧定时器面板已被升级版定时任务系统替代
            # 升级版系统有自己的清理机制，无需手动清理

            self.parent_ui.log("✅ 定时器线程已清理（升级版系统自动管理）")
            
        except Exception as e:
            print(f"清理定时器线程失败: {e}")

    def cleanup_browser_processes(self):
        """清理浏览器进程（仅关闭本应用创建的浏览器）"""
        try:
            # 仅关闭本应用注册过的浏览器实例
            try:
                from 网易号存稿.browser.driver import DriverRegistry
                DriverRegistry.close_all(self.parent_ui.log)
            except Exception:
                pass

            # 如仍需兜底强制清理，可在设置中开启（默认关闭，避免误杀其他浏览器）
            if getattr(self.parent_ui, 'force_kill_browsers_on_exit', False):
                self.force_cleanup_chrome_processes()

            self.parent_ui.log("✅ 浏览器进程已清理（仅本应用创建的实例）")

        except Exception as e:
            print(f"清理浏览器进程失败: {e}")
    
    def force_cleanup_chrome_processes(self):
        """强制清理Chrome进程"""
        try:
            import subprocess
            import platform
            
            system = platform.system()
            
            if system == "Windows":
                # Windows系统清理Chrome进程
                try:
                    subprocess.run(
                        ["taskkill", "/f", "/im", "chrome.exe"],
                        capture_output=True,
                        timeout=10
                    )
                    subprocess.run(
                        ["taskkill", "/f", "/im", "chromedriver.exe"],
                        capture_output=True,
                        timeout=10
                    )
                except subprocess.TimeoutExpired:
                    pass
            elif system in ["Linux", "Darwin"]:
                # Linux/Mac系统清理Chrome进程
                try:
                    subprocess.run(
                        ["pkill", "-f", "chrome"],
                        capture_output=True,
                        timeout=10
                    )
                    subprocess.run(
                        ["pkill", "-f", "chromedriver"],
                        capture_output=True,
                        timeout=10
                    )
                except subprocess.TimeoutExpired:
                    pass
            
            self.parent_ui.log("✅ Chrome进程强制清理完成")
            
        except Exception as e:
            print(f"强制清理Chrome进程失败: {e}")

    def cleanup_log_handler(self):
        """清理日志处理线程"""
        try:
            # 停止日志管理器
            if hasattr(self.parent_ui, 'log_manager'):
                self.parent_ui.log_manager.cleanup()

            # 停止日志处理线程
            if hasattr(self.parent_ui, 'log_handler_running'):
                self.parent_ui.log_handler_running = False

            self.parent_ui.log("✅ 日志处理线程已清理")

        except Exception as e:
            print(f"清理日志处理线程失败: {e}")
    
    def cleanup_draft_processors(self):
        """清理存稿处理器资源"""
        try:
            # 清理网易号存稿处理器
            if hasattr(self.parent_ui, 'netease_processor'):
                processor = self.parent_ui.netease_processor
                if hasattr(processor, 'cleanup'):
                    processor.cleanup()
            
            # 清理头条存稿处理器
            if hasattr(self.parent_ui, 'toutiao_processor'):
                processor = self.parent_ui.toutiao_processor
                if hasattr(processor, 'cleanup'):
                    processor.cleanup()
            
            # 清理大鱼号存稿处理器
            if hasattr(self.parent_ui, 'dayu_processor'):
                processor = self.parent_ui.dayu_processor
                if hasattr(processor, 'cleanup'):
                    processor.cleanup()
            
            self.parent_ui.log("✅ 存稿处理器已清理")
            
        except Exception as e:
            print(f"清理存稿处理器失败: {e}")

    def cleanup_thread_pools(self):
        """清理线程池资源"""
        try:
            # 清理主线程池
            if hasattr(self.parent_ui, 'thread_pool'):
                thread_pool = self.parent_ui.thread_pool
                if hasattr(thread_pool, 'shutdown'):
                    thread_pool.shutdown(wait=False)

            # 清理并发管理器的线程池
            if hasattr(self.parent_ui, 'concurrent_manager'):
                concurrent_manager = self.parent_ui.concurrent_manager
                if hasattr(concurrent_manager, 'cleanup'):
                    concurrent_manager.cleanup()

            self.parent_ui.log("✅ 线程池已清理")

        except Exception as e:
            print(f"清理线程池失败: {e}")
    
    def force_stop_worker_threads(self):
        """强制停止所有工作线程"""
        try:
            # 停止串行刷新工作线程
            if hasattr(self.parent_ui, 'stop_refresh_worker'):
                self.parent_ui.log("正在停止串行刷新工作线程...")
                self.parent_ui.stop_refresh_worker()

            # 停止所有可能的工作线程
            active_threads = threading.active_count()

            # 等待线程结束（最多等待3秒）
            start_time = time.time()
            while threading.active_count() > 1 and (time.time() - start_time) < 3:
                time.sleep(0.1)

            remaining_threads = threading.active_count()
            if remaining_threads > 1:
                self.parent_ui.log(f"⚠️ 仍有 {remaining_threads - 1} 个线程未结束")
            else:
                self.parent_ui.log("✅ 所有工作线程已停止")

        except Exception as e:
            print(f"强制停止工作线程失败: {e}")

    def cleanup_temp_files(self):
        """清理临时文件和缓存"""
        try:
            # 清理截图临时文件
            screenshot_dir = self.config_manager.get("screenshot_dir", "")
            if screenshot_dir and os.path.exists(screenshot_dir):
                temp_files = glob.glob(os.path.join(screenshot_dir, "temp_*.png"))
                for temp_file in temp_files:
                    try:
                        os.remove(temp_file)
                    except:
                        pass

            # 清理浏览器缓存目录
            cache_dirs = [
                os.path.expanduser("~/AppData/Local/Temp/chrome_*"),
                "/tmp/chrome_*",
                "~/.cache/chrome_*"
            ]

            for cache_pattern in cache_dirs:
                try:
                    cache_files = glob.glob(cache_pattern)
                    for cache_file in cache_files:
                        if os.path.isdir(cache_file):
                            import shutil
                            shutil.rmtree(cache_file, ignore_errors=True)
                except:
                    pass

            self.parent_ui.log("✅ 临时文件已清理")

        except Exception as e:
            print(f"清理临时文件失败: {e}")
    
    def force_terminate_subprocesses(self):
        """强制终止当前进程的子进程"""
        try:
            import subprocess
            import psutil
            import os
            
            current_pid = os.getpid()
            current_process = psutil.Process(current_pid)
            
            # 获取所有子进程
            children = current_process.children(recursive=True)
            
            # 终止子进程
            for child in children:
                try:
                    child.terminate()
                except psutil.NoSuchProcess:
                    pass
            
            # 等待子进程结束
            gone, alive = psutil.wait_procs(children, timeout=3)
            
            # 强制杀死仍然存活的进程
            for process in alive:
                try:
                    process.kill()
                except psutil.NoSuchProcess:
                    pass
            
            if children:
                self.parent_ui.log(f"✅ 已终止 {len(children)} 个子进程")
            
        except ImportError:
            # 如果没有psutil，使用基本方法
            try:
                import subprocess
                import platform
                
                if platform.system() == "Windows":
                    subprocess.run(
                        ["taskkill", "/f", "/t", "/pid", str(os.getpid())],
                        capture_output=True,
                        timeout=5
                    )
            except:
                pass
        except Exception as e:
            print(f"强制终止子进程失败: {e}")

    def cleanup_after_draft_task(self):
        """存稿任务完成后的资源清理"""
        try:
            self.parent_ui.log("开始清理存稿任务相关资源...")

            # 清理浏览器驱动
            self.cleanup_browser_processes()

            # 清理临时文件
            self.cleanup_temp_files()

            # 重置任务状态
            if hasattr(self.parent_ui, 'is_running'):
                self.parent_ui.is_running = False

            # 重置进度显示
            if hasattr(self.parent_ui, 'account_progress'):
                self.parent_ui.account_progress = {}

            self.parent_ui.log("✅ 存稿任务资源清理完成")

        except Exception as e:
            print(f"存稿任务后清理失败: {e}")
    
    def create_required_dirs(self):
        """创建必要的目录"""
        try:
            current_platform = getattr(self.parent_ui, 'current_platform', 'netease')
            
            # 需要创建的目录列表
            dirs_to_create = [
                self.config_manager.get("account_dir", "", platform=current_platform),
                self.config_manager.get("video_dir", "", platform=current_platform),
                self.config_manager.get("screenshot_dir", "", platform=current_platform),
                self.config_manager.get_config_dir()
            ]
            
            created_dirs = []
            for dir_path in dirs_to_create:
                if dir_path and not os.path.exists(dir_path):
                    try:
                        os.makedirs(dir_path, exist_ok=True)
                        created_dirs.append(dir_path)
                    except Exception as e:
                        print(f"创建目录 {dir_path} 失败: {e}")

            if created_dirs:
                self.parent_ui.log(f"✅ 已创建 {len(created_dirs)} 个必要目录")

        except Exception as e:
            print(f"创建必要目录失败: {e}")
    
    def backup_account_data(self):
        """备份账号数据"""
        try:
            from tkinter import filedialog
            import datetime
            import shutil
            
            # 选择备份位置
            backup_dir = filedialog.askdirectory(title="选择备份目录")
            if not backup_dir:
                return
            
            # 创建备份文件夹
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_folder = os.path.join(backup_dir, f"账号数据备份_{timestamp}")
            os.makedirs(backup_folder, exist_ok=True)
            
            # 备份账号目录
            current_platform = getattr(self.parent_ui, 'current_platform', 'netease')
            account_dir = self.config_manager.get("account_dir", "", platform=current_platform)
            
            if account_dir and os.path.exists(account_dir):
                shutil.copytree(account_dir, os.path.join(backup_folder, "accounts"))
            
            # 备份配置文件
            config_dir = self.config_manager.get_config_dir()
            if os.path.exists(config_dir):
                shutil.copytree(config_dir, os.path.join(backup_folder, "config"))
            
            self.parent_ui.log(f"✅ 账号数据已备份到: {backup_folder}")
            
        except Exception as e:
            error_msg = f"备份账号数据失败: {str(e)}"
            self.parent_ui.log(f"❌ {error_msg}")
            print(error_msg)
    
    def cleanup(self):
        """清理资源管理器本身"""
        try:
            # 等待所有清理线程结束
            for thread in self.cleanup_threads:
                if thread.is_alive():
                    thread.join(timeout=1)
            
            self.cleanup_threads.clear()
            
        except Exception as e:
            print(f"清理资源管理器失败: {e}")
