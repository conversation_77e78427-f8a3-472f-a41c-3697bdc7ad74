"""
账号管理面板模块 - 负责显示和管理账号
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Callable, Dict, Any, List, Optional
import threading
import os

from ...account.manager import AccountManager
from ...account.data import AccountData

class AccountPanel:
    """账号管理面板类，负责显示和管理账号"""

    def __init__(self, parent: ttk.Frame, account_manager: AccountManager, account_data: AccountData, log_callback: Callable = None,
                 data_query_manager=None, config_manager=None, current_platform=None):
        """
        初始化账号管理面板

        Args:
            parent: 父容器
            account_manager: 账号管理器
            account_data: 账号数据处理器
            log_callback: 日志回调函数
            data_query_manager: 数据查询管理器
            config_manager: 配置管理器
            current_platform: 当前平台
        """
        self.parent = parent
        self.account_manager = account_manager
        self.account_data = account_data
        self.log_callback = log_callback
        self.data_query_manager = data_query_manager
        self.config_manager = config_manager
        self.current_platform = current_platform

        # 初始化变量
        self.selected_account = tk.StringVar()
        self.account_count = tk.StringVar(value="0")

        # 创建UI
        self.create_ui()

        # 创建右键菜单
        self.create_context_menu()

        # 加载账号列表
        self.load_accounts()

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def create_ui(self) -> None:
        """创建用户界面"""
        # 账号工具栏
        toolbar = ttk.Frame(self.parent)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        # 账号计数显示
        count_label = ttk.Label(toolbar, text="账号数量:")
        count_label.pack(side=tk.LEFT, padx=5)
        account_count_label = ttk.Label(toolbar, textvariable=self.account_count)
        account_count_label.pack(side=tk.LEFT, padx=5)

        # 账号管理按钮
        ttk.Button(toolbar, text="添加账号", command=self.add_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="删除选中", command=self.delete_selected_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="导出Excel", command=self.export_accounts_to_excel).pack(side=tk.LEFT, padx=5)

        # 数据查询按钮
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=10, fill=tk.Y)
        ttk.Button(toolbar, text="查询数据", command=self.start_data_query).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="停止查询", command=self.stop_data_query).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="账号数据查看", command=self.show_collected_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="备份数据", command=self.backup_account_data).pack(side=tk.LEFT, padx=5)

        # 账号列表框架
        account_list_frame = ttk.Frame(self.parent)
        account_list_frame.pack(fill=tk.BOTH, expand=True)

        # 账号列表（使用Treeview）
        columns = ("序号", "账号", "用户名", "七日收益", "总收益", "昨日收益", "总播放", "昨日播放", "总粉丝", "昨日粉丝", "状态", "更新时间")
        self.account_tree = ttk.Treeview(account_list_frame, columns=columns, show="headings", height=20)

        # 设置列标题和宽度
        for col in columns:
            self.account_tree.heading(col, text=col)
            if col == "序号":
                self.account_tree.column(col, width=50, anchor=tk.CENTER)
            elif col == "账号":
                self.account_tree.column(col, width=150, anchor=tk.W)
            elif col == "用户名":
                self.account_tree.column(col, width=150, anchor=tk.W)
            elif col == "总收益" or col == "昨日收益" or col == "七日收益":
                self.account_tree.column(col, width=120, anchor=tk.CENTER)
            elif col == "更新时间":
                self.account_tree.column(col, width=150, anchor=tk.CENTER)
            elif col == "状态":
                self.account_tree.column(col, width=80, anchor=tk.CENTER)
            elif col == "总粉丝" or col == "昨日粉丝":
                self.account_tree.column(col, width=100, anchor=tk.CENTER)
            else:
                self.account_tree.column(col, width=100, anchor=tk.CENTER)

        # 删除垂直滚动条，只保留鼠标滚轮滚动
        # 不再配置滚动条命令

        # 直接填充整个区域
        self.account_tree.pack(fill=tk.BOTH, expand=True)

        # 绑定鼠标滚轮事件以支持滚动
        def _on_mousewheel(event):
            self.account_tree.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            self.account_tree.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            self.account_tree.unbind_all("<MouseWheel>")

        self.account_tree.bind('<Enter>', _bind_mousewheel)
        self.account_tree.bind('<Leave>', _unbind_mousewheel)

        # 绑定事件
        self.account_tree.bind("<Double-1>", self.on_account_select)  # 双击选择账号
        self.account_tree.bind("<Button-3>", self.show_account_menu)  # 右键菜单

    def create_context_menu(self) -> None:
        """创建右键菜单"""
        self.account_menu = tk.Menu(self.parent, tearoff=0)
        self.account_menu.add_command(label="复制账号", command=self.copy_account_name)
        self.account_menu.add_command(label="复制用户名", command=self.copy_username)
        self.account_menu.add_separator()
        self.account_menu.add_command(label="查看详情", command=lambda: self.show_account_details_from_menu())
        # 添加查询数据选项
        self.account_menu.add_separator()
        self.account_menu.add_command(label="查询数据", command=self.query_selected_account_data)
        # 添加登录账号选项（头条和网易平台）
        if self.current_platform in ["toutiao", "netease"]:
            self.account_menu.add_separator()
            self.account_menu.add_command(label="登录账号", command=self.login_selected_account_from_menu)
        # 添加删除账号选项
        self.account_menu.add_separator()
        self.account_menu.add_command(label="删除账号", command=self.delete_selected_account)

    def load_accounts(self) -> None:
        """加载账号列表"""
        # 加载账号
        accounts = self.account_manager.load_accounts()

        # 更新账号数量显示
        self.account_count.set(str(len(accounts)))

        # 更新账号列表
        self.update_account_tree()

    def update_account_tree(self) -> None:
        """更新账号列表树"""
        # 清空现有项目
        for item in self.account_tree.get_children():
            self.account_tree.delete(item)

        # 获取当前账号目录中实际存在的账号列表
        existing_accounts = set(self.account_manager.load_accounts())

        # 获取所有账号数据（只包含实际存在的账号）
        accounts_data_dict = {}
        for account_data in self.account_data.get_all_accounts_data(existing_accounts):
            accounts_data_dict[account_data.get("账号", "")] = account_data

        # 确保所有已加载的账号都显示，即使没有数据
        accounts_data = []
        for account in existing_accounts:
            if account in accounts_data_dict:
                # 使用已有数据
                accounts_data.append(accounts_data_dict[account])
            else:
                # 创建默认数据
                default_data = {
                    "账号": account,
                    "用户名": "",
                    "总收益": "0",
                    "累计收益": "0",
                    "昨日收益": "0",
                    "总播放": "0",
                    "昨日播放": "0",
                    "总粉丝": "0",
                    "昨日粉丝": "0",
                    "草稿箱": "0",
                    "状态": "未查询",
                    "更新时间": ""
                }
                accounts_data.append(default_data)

        # 初始化总计数据
        total_seven_day_income = 0
        total_income = 0
        total_yesterday_income = 0
        total_plays = 0
        total_yesterday_plays = 0
        total_fans = 0
        total_yesterday_fans = 0

        # 使用账号数据填充树（现在所有账号都有数据，包括默认数据）
        for i, data in enumerate(accounts_data):
            # 计算七日收益总和
            seven_day_income = 0
            if "七日收益数据" in data:
                for income in data["七日收益数据"].values():
                    income_value = float(income) if isinstance(income, (int, float, str)) else 0
                    seven_day_income += income_value

            # 解析数值数据，修复字段映射
            try:
                # 头条平台使用"累计收益"，其他平台使用"总收益"
                current_income = float(str(data.get("累计收益", data.get("总收益", "0"))).replace(",", ""))
            except:
                current_income = 0

            try:
                yesterday_income = float(str(data.get("昨日收益", "0")).replace(",", ""))
            except:
                yesterday_income = 0

            try:
                plays = float(str(data.get("总播放", "0")).replace(",", ""))
            except:
                plays = 0

            try:
                yesterday_plays = float(str(data.get("昨日播放", "0")).replace(",", ""))
            except:
                yesterday_plays = 0

            try:
                fans = float(str(data.get("总粉丝", "0")).replace(",", ""))
            except:
                fans = 0

            try:
                yesterday_fans = float(str(data.get("昨日粉丝", "0")).replace(",", ""))
            except:
                yesterday_fans = 0

            # 累加总计
            total_seven_day_income += seven_day_income
            total_income += current_income
            total_yesterday_income += yesterday_income
            total_plays += plays
            total_yesterday_plays += yesterday_plays
            total_fans += fans
            total_yesterday_fans += yesterday_fans

            # 插入账号数据行，修复字段映射
            self.account_tree.insert("", "end", values=(
                i+1,
                data.get("账号", ""),
                data.get("用户名", ""),
                f"{seven_day_income:.2f}",
                data.get("累计收益", data.get("总收益", "")),  # 修复总收益字段映射
                data.get("昨日收益", ""),
                data.get("总播放", ""),
                data.get("昨日播放", ""),
                data.get("总粉丝", ""),
                data.get("昨日粉丝", ""),
                data.get("状态", "未查询"),
                data.get("更新时间", "")
            ))

        # 添加总计行
        self.account_tree.insert("", "end", values=(
                "",
                "总计",
                "",
                f"{total_seven_day_income:.2f}",
                f"{total_income:.2f}",
                f"{total_yesterday_income:.2f}",
                f"{total_plays:.0f}",
                f"{total_yesterday_plays:.0f}",
                f"{total_fans:.0f}",
                f"{total_yesterday_fans:.0f}",
                "",
                ""
        ), tags=("total",))

        # 设置总计行样式
        self.account_tree.tag_configure("total", background="#E0E0E0", font=("Arial", 10, "bold"))

    def on_account_select(self, event=None) -> None:
        """
        账号选择事件处理

        Args:
            event: 事件对象
        """
        # 获取选中的项目
        selected_items = self.account_tree.selection()
        if not selected_items:
            return

        # 获取选中的账号
        item = selected_items[0]
        values = self.account_tree.item(item, "values")
        if len(values) < 2:
            return

        account = values[1]  # 账号列的索引为1

        # 设置选中的账号
        self.selected_account.set(account)

        # 触发选中事件
        self.on_account_selected(account)

        self.log(f"已选择账号: {account}")

    def on_account_selected(self, account: str) -> None:
        """
        账号选中事件处理

        Args:
            account: 选中的账号
        """
        # 这个方法可以被子类重写，用于处理账号选中事件
        pass

    def show_account_menu(self, event) -> None:
        """
        显示账号右键菜单

        Args:
            event: 事件对象
        """
        # 获取点击位置的项目
        item = self.account_tree.identify_row(event.y)
        if not item:
            return

        # 选中该项目
        self.account_tree.selection_set(item)

        # 显示菜单
        self.account_menu.post(event.x_root, event.y_root)

    def copy_account_name(self) -> None:
        """复制账号名称到剪贴板"""
        # 获取选中的项目
        selected_items = self.account_tree.selection()
        if not selected_items:
            return

        # 获取选中的账号
        item = selected_items[0]
        values = self.account_tree.item(item, "values")
        if len(values) < 2:
            return

        account = values[1]  # 账号列的索引为1

        # 复制到剪贴板
        self.parent.clipboard_clear()
        self.parent.clipboard_append(account)

        self.log(f"已复制账号: {account}")

    def add_account(self) -> None:
        """添加账号"""
        # 这个方法可以被子类重写，用于实现添加账号功能
        self.log("添加账号功能尚未实现")

    def delete_selected_account(self) -> None:
        """删除选中的账号"""
        # 获取选中的项目
        selected_items = self.account_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要删除的账号")
            return

        # 获取选中的账号
        item = selected_items[0]
        values = self.account_tree.item(item, "values")
        if len(values) < 2:
            return

        account = values[1]  # 账号列的索引为1

        # 确认删除
        if not messagebox.askyesno("确认删除", f"确定要删除账号 {account} 吗？"):
            return

        # 删除账号
        if self.account_manager.delete_account(account):
            self.log(f"账号 {account} 删除成功")

            # 如果删除的是当前选中的账号，清除选中状态
            if self.selected_account.get() == account:
                self.selected_account.set("")

            # 更新账号列表
            self.load_accounts()
        else:
            self.log(f"账号 {account} 删除失败")

    def export_accounts_to_excel(self) -> None:
        """导出账号数据到Excel"""
        # 这个方法可以被子类重写，用于实现导出Excel功能
        self.log("导出Excel功能尚未实现")

    def start_data_query(self) -> None:
        """开始数据查询"""
        # 这个方法可以被子类重写，用于实现数据查询功能
        self.log("数据查询功能尚未实现")

    def stop_data_query(self) -> None:
        """停止数据查询"""
        # 这个方法可以被子类重写，用于实现停止数据查询功能
        self.log("停止数据查询功能尚未实现")

    def show_collected_data(self) -> None:
        """显示已收集的数据"""
        # 这个方法可以被子类重写，用于实现显示数据功能
        self.log("显示数据功能尚未实现")

    def backup_account_data(self) -> None:
        """备份账号数据"""
        # 这个方法可以被子类重写，用于实现备份数据功能
        self.log("备份数据功能尚未实现")

    def copy_username(self):
        """复制用户名到剪贴板"""
        # 获取选中的项目
        selected_items = self.account_tree.selection()
        if not selected_items:
            return

        # 获取选中的用户名
        item = selected_items[0]
        values = self.account_tree.item(item, "values")
        if len(values) < 3:
            return

        username = values[2]  # 用户名列的索引为2（主界面账号列表中）

        if not username or username == "":
            self.log("该账号没有用户名信息")
            return

        # 复制到剪贴板
        self.parent.clipboard_clear()
        self.parent.clipboard_append(username)

        self.log(f"已复制用户名: {username}")

    def query_selected_account_data(self):
        """查询选中账号的数据（右键菜单）"""
        # 获取选中的项目
        selected_items = self.account_tree.selection()
        if not selected_items:
            messagebox.showwarning("提示", "请先选择要查询的账号")
            return

        # 获取选中的账号
        item = selected_items[0]
        values = self.account_tree.item(item, "values")
        if len(values) < 2:
            return

        account = values[1]  # 账号列的索引为1（主界面账号列表中）
        if account == "总计":
            messagebox.showwarning("提示", "无法查询总计行数据")
            return

        # 检查是否有数据查询管理器
        if not self.data_query_manager:
            messagebox.showerror("错误", "数据查询管理器未初始化")
            return

        # 启动单账号查询
        try:
            self.data_query_manager.start_single_account_query(account)
            self.log(f"🔍 已启动账号 {account} 的数据查询")
        except Exception as e:
            self.log(f"❌ 启动账号 {account} 数据查询失败: {str(e)}")
            messagebox.showerror("查询失败", f"启动账号 {account} 数据查询失败:\n{str(e)}")

    def login_selected_account_from_menu(self):
        """登录选中的账号（右键菜单）"""
        # 获取选中的项目
        selected_items = self.account_tree.selection()
        if not selected_items:
            return

        # 获取选中的账号
        item = selected_items[0]
        values = self.account_tree.item(item, "values")
        if len(values) < 2:
            return

        account = values[1]  # 账号列的索引为1（主界面账号列表中）
        if account == "总计":
            return

        self._login_account(account)

    def _login_account(self, account):
        """登录指定账号"""
        if self.current_platform not in ["toutiao", "netease", "dayu"]:
            messagebox.showwarning("警告", "当前平台不支持登录功能")
            return

        # 根据平台显示不同的确认信息
        if self.current_platform == "toutiao":
            platform_name = "头条"
            login_url = "https://mp.toutiao.com/auth/page/login"
        elif self.current_platform == "dayu":
            platform_name = "大鱼号"
            login_url = "https://mp.dayu.com/"
        else:  # netease
            platform_name = "网易号"
            login_url = "https://mp.163.com/"

        # 已移除“确认登录”弹窗，直接开始登录以减少打扰

        self.log(f"正在登录{platform_name}账号: {account}")

        # 在后台线程中执行登录，避免阻塞UI
        threading.Thread(target=self._login_account_thread, args=(account,), daemon=True).start()

    def _login_account_thread(self, account):
        """在后台线程中执行登录"""
        if self.current_platform == "toutiao":
            self._login_toutiao_account_thread(account)
        elif self.current_platform == "dayu":
            self._login_dayu_account_thread(account)
        elif self.current_platform == "netease":
            self._login_netease_account_thread(account)

    def _login_toutiao_account_thread(self, account):
        """在后台线程中执行头条账号登录"""
        try:
            # 导入必要的模块
            from 网易号存稿.platforms.toutiao.login import ToutiaoLogin

            # 创建登录对象
            toutiao_login = ToutiaoLogin(self.log)

            # 获取账号目录 - 与存稿和查询功能保持一致
            if hasattr(self, 'account_manager') and self.account_manager and hasattr(self.account_manager, 'account_dir'):
                account_dir = self.account_manager.account_dir
            else:
                # 兼容旧的硬编码路径
                account_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "账号")

            # 查找Cookie文件 - 只查找账号名.txt格式
            cookie_path = os.path.join(account_dir, f"{account}.txt")
            if not os.path.exists(cookie_path):
                cookie_path = None

            if not cookie_path:
                self.log(f"❌ 头条账号 {account} Cookie文件不存在")
                return

            # 使用Cookie登录
            success, driver = toutiao_login.login_with_cookies(cookie_path, headless=False, account=account)

            if success:
                self.log(f"✅ 头条账号 {account} 登录成功")
            else:
                self.log(f"❌ 头条账号 {account} 登录失败")

                # 在主线程中询问是否尝试手机号登录
                self.parent.after(0, lambda: self._ask_for_toutiao_phone_login(account, toutiao_login, cookie_path))

        except Exception as e:
            self.log(f"❌ 头条账号 {account} 登录异常: {str(e)}")

    def _login_dayu_account_thread(self, account):
        """在后台线程中执行大鱼号账号登录"""
        try:
            # 导入必要的模块
            from 网易号存稿.platforms.dayu.login import DayuLogin

            # 创建登录对象
            dayu_login = DayuLogin(self.log)

            # 获取账号目录 - 与存稿和查询功能保持一致
            if hasattr(self, 'account_manager') and self.account_manager and hasattr(self.account_manager, 'account_dir'):
                account_dir = self.account_manager.account_dir
            else:
                # 兼容旧的硬编码路径
                account_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "账号")

            # 查找Cookie文件 - 只查找账号名.txt格式
            cookie_path = os.path.join(account_dir, f"{account}.txt")
            if not os.path.exists(cookie_path):
                cookie_path = None

            if not cookie_path:
                self.log(f"❌ 大鱼号账号 {account} Cookie文件不存在")
                return

            # 使用Cookie登录
            success, driver = dayu_login.login_with_cookies(cookie_path, headless=False, account=account)

            if success:
                self.log(f"✅ 大鱼号账号 {account} 登录成功")
            else:
                self.log(f"❌ 大鱼号账号 {account} 登录失败")

                # 在主线程中询问是否尝试手机号登录
                self.parent.after(0, lambda: self._ask_for_dayu_phone_login(account, dayu_login, cookie_path))

        except Exception as e:
            self.log(f"❌ 大鱼号账号 {account} 登录异常: {str(e)}")

    def _login_netease_account_thread(self, account):
        """在后台线程中执行网易号账号登录"""
        try:
            # 导入必要的模块
            from 网易号存稿.account.login import AccountLogin

            # 创建登录对象
            netease_login = AccountLogin(log_callback=self.log)

            # 获取账号目录 - 与存稿和查询功能保持一致
            if hasattr(self, 'account_manager') and self.account_manager and hasattr(self.account_manager, 'account_dir'):
                account_dir = self.account_manager.account_dir
            else:
                # 兼容旧的硬编码路径
                account_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "账号")

            # 查找Cookie文件 - 只查找账号名.txt格式
            cookie_path = os.path.join(account_dir, f"{account}.txt")
            if not os.path.exists(cookie_path):
                self.log(f"❌ 网易号账号 {account} Cookie文件不存在")
                return

            # 使用Cookie登录
            success, driver = netease_login.login_with_cookies(cookie_path, headless=False, account=account)

            if success:
                self.log(f"✅ 网易号账号 {account} 登录成功")
            else:
                self.log(f"❌ 网易号账号 {account} 登录失败")

                # 在主线程中询问是否尝试手机号登录
                self.parent.after(0, lambda: self._ask_for_netease_phone_login(account, netease_login, cookie_path))

        except Exception as e:
            self.log(f"❌ 网易号账号 {account} 登录异常: {str(e)}")

    def show_account_details_from_menu(self):
        """从右键菜单显示账号详情"""
        # 获取选中的项目
        selected_items = self.account_tree.selection()
        if not selected_items:
            return

        # 获取选中的账号
        item = selected_items[0]
        values = self.account_tree.item(item, "values")
        if len(values) < 2:
            return

        account = values[1]  # 账号列的索引为1（主界面账号列表中）
        if account == "总计":
            return

        # 显示账号详情窗口
        self.show_account_details_window(account)

    def show_account_details_window(self, account):
        """
        显示独立的账号详情窗口

        【作废计划 目前仍在使用 但不更新】
        注意：此方法为账号详情窗口显示的实现，虽然标记为作废计划，但目前仍在使用中。
        该方法不再进行功能更新和维护，仅保持现有详情窗口功能的稳定运行。
        """
        # 查找账号数据
        account_data = None
        all_accounts_data = self.account_data.get_all_accounts_data()

        for data in all_accounts_data:
            if data.get("账号") == account:
                account_data = data
                break

        if not account_data:
            messagebox.showwarning("警告", f"未找到账号 {account} 的数据")
            return

        # 创建详情窗口
        details_window = tk.Toplevel(self.parent)
        details_window.title(f"账号详情 - {account}")
        details_window.geometry("800x600")
        details_window.minsize(600, 400)
        details_window.configure(bg="#f8f9fa")

        # 设置窗口图标
        try:
            details_window.iconbitmap(self.parent.iconbitmap())
        except:
            pass

        # 创建主框架
        main_frame = tk.Frame(details_window, bg="#ffffff", relief=tk.RAISED, bd=1)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题
        title_label = tk.Label(main_frame, text=f"账号详情 - {account}",
                              font=("Microsoft YaHei", 16, "bold"),
                              bg="#ffffff", fg="#2c3e50")
        title_label.pack(pady=(0, 20))

        # 创建详情内容
        self._create_account_details_content(main_frame, account_data)

        # 底部按钮
        button_frame = tk.Frame(main_frame, bg="#ffffff")
        button_frame.pack(fill=tk.X, pady=(20, 0))

        # 窗口关闭时确保解绑滚轮事件
        def on_window_close():
            try:
                canvas.unbind_all("<MouseWheel>")
            except:
                pass
            details_window.destroy()

        details_window.protocol("WM_DELETE_WINDOW", on_window_close)

        # 关闭按钮
        close_btn = ttk.Button(button_frame, text="关闭", command=on_window_close)
        close_btn.pack(side=tk.RIGHT)

        # 居中显示窗口
        self._center_window(details_window)

    def _create_account_details_content(self, parent, account_data):
        """创建账号详情内容"""
        # 创建滚动框架（删除滚动条）
        canvas = tk.Canvas(parent, bg="#ffffff")
        scrollable_frame = tk.Frame(canvas, bg="#ffffff")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

        # 绑定鼠标滚轮事件以支持滚动
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind('<Enter>', _bind_mousewheel)
        canvas.bind('<Leave>', _unbind_mousewheel)

        # 基本信息卡片
        info_frame = tk.LabelFrame(scrollable_frame, text="基本信息",
                                  font=("Microsoft YaHei", 12, "bold"),
                                  bg="#ffffff", fg="#2c3e50", relief=tk.RAISED, bd=2)
        info_frame.pack(fill=tk.X, padx=10, pady=10)

        # 显示账号信息
        info_items = [
            ("账号", account_data.get("账号", "")),
            ("用户名", account_data.get("用户名", "")),
            ("总收益", account_data.get("总收益", "")),
            ("昨日收益", account_data.get("昨日收益", "")),
            ("七日收益", account_data.get("七日收益", "")),
            ("总播放", account_data.get("总播放", "")),
            ("昨日播放", account_data.get("昨日播放", "")),
            ("总粉丝", account_data.get("总粉丝", "")),
            ("昨日粉丝", account_data.get("昨日粉丝", "")),
            ("状态", account_data.get("状态", "")),
            ("更新时间", account_data.get("更新时间", ""))
        ]

        for i, (label, value) in enumerate(info_items):
            row_frame = tk.Frame(info_frame, bg="#ffffff")
            row_frame.pack(fill=tk.X, padx=10, pady=5)

            label_widget = tk.Label(row_frame, text=f"{label}:",
                                   font=("Microsoft YaHei", 10, "bold"),
                                   bg="#ffffff", fg="#34495e", width=12, anchor="w")
            label_widget.pack(side=tk.LEFT)

            value_widget = tk.Label(row_frame, text=str(value),
                                   font=("Microsoft YaHei", 10),
                                   bg="#ffffff", fg="#2c3e50", anchor="w")
            value_widget.pack(side=tk.LEFT, fill=tk.X, expand=True)

        canvas.pack(fill="both", expand=True)

    def _center_window(self, window):
        """居中显示窗口"""
        window.update_idletasks()
        width = window.winfo_width()
        height = window.winfo_height()
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        window.geometry(f"{width}x{height}+{x}+{y}")

    def _ask_for_toutiao_phone_login(self, account, toutiao_login, cookie_path):
        """询问是否尝试头条号手机号登录"""
        from tkinter import messagebox

        # 直接跳过所有登录确认弹窗，自动尝试手机号登录
        self.log(f"正在尝试使用手机号登录头条账号: {account}")
        import threading
        threading.Thread(target=self._toutiao_phone_login_thread, args=(account, toutiao_login, cookie_path), daemon=True).start()

    def _toutiao_phone_login_thread(self, account, toutiao_login, cookie_path):
        """在后台线程中执行头条号手机号登录"""
        try:
            # 使用手机号登录
            login_success, driver, cookies = toutiao_login.login_with_phone(False)

            if login_success and cookies:
                self.log(f"✅ 头条账号 {account} 手机号登录成功")

                # 在主线程中询问是否更新Cookie
                self.parent.after(0, lambda: self._ask_for_toutiao_cookie_update(account, cookies, cookie_path, driver))
            else:
                self.log(f"❌ 头条账号 {account} 手机号登录失败")
                self.parent.after(0, lambda: messagebox.showerror("登录失败", f"头条账号 {account} 手机号登录失败"))

                # 如果浏览器仍然活动，提示用户可以手动关闭
                if driver:
                    self.log(f"浏览器仍然处于打开状态，您可以随时手动关闭")
                    self.parent.after(0, lambda: messagebox.showinfo("提示", "浏览器仍然处于打开状态，您可以随时手动关闭。"))

        except Exception as e:
            self.log(f"❌ 头条账号 {account} 手机号登录过程中发生错误: {str(e)}")
            self.parent.after(0, lambda: messagebox.showerror("错误", f"头条账号 {account} 手机号登录过程中发生错误: {str(e)}"))
            import traceback
            traceback.print_exc()

    def _ask_for_toutiao_cookie_update(self, account, cookies, cookie_path, driver):
        """询问是否更新头条号Cookie"""
        from tkinter import messagebox

        if messagebox.askyesno("更新Cookie", f"头条账号 {account} 手机号登录成功！\n\n是否将新的Cookie保存到文件中？\n\n这将覆盖现有的Cookie文件。"):
            try:
                # 保存Cookie到文件
                import json
                with open(cookie_path, 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, ensure_ascii=False, indent=2)

                self.log(f"✅ 头条账号 {account} Cookie已更新保存")
                messagebox.showinfo("保存成功", f"头条账号 {account} 的Cookie已成功保存到文件中。")

            except Exception as e:
                self.log(f"❌ 保存Cookie失败: {str(e)}")
                messagebox.showerror("保存失败", f"保存Cookie时发生错误: {str(e)}")
        else:
            self.log(f"跳过Cookie保存")

        # 关闭浏览器
        if driver:
            try:
                driver.quit()
            except:
                pass

    def _ask_for_dayu_phone_login(self, account, dayu_login, cookie_path):
        """询问是否尝试大鱼号手机号登录"""
        from tkinter import messagebox

        if not messagebox.askyesno("登录失败", f"大鱼号账号 {account} Cookie登录失败，可能Cookie已过期。\n\n是否尝试使用手机号登录？"):
            messagebox.showerror("登录失败", f"大鱼号账号 {account} 登录失败")
            return

        self.log(f"正在尝试使用手机号登录大鱼号账号: {account}")

        # 显示提示对话框
        messagebox.showinfo("操作提示", "请在打开的浏览器中完成大鱼号账号登录操作。\n\n登录地址：https://mp.dayu.com/\n\n浏览器将保持打开状态，您可以随时手动关闭。")

        # 在后台线程中执行手机号登录
        import threading
        threading.Thread(target=self._dayu_phone_login_thread, args=(account, dayu_login, cookie_path), daemon=True).start()

    def _dayu_phone_login_thread(self, account, dayu_login, cookie_path):
        """在后台线程中执行大鱼号手机号登录"""
        try:
            # 使用手机号登录
            login_success, driver, cookies = dayu_login.login_with_phone(False)

            if login_success and cookies:
                self.log(f"✅ 大鱼号账号 {account} 手机号登录成功")

                # 在主线程中询问是否更新Cookie
                self.parent.after(0, lambda: self._ask_for_dayu_cookie_update(account, cookies, cookie_path, driver))
            else:
                self.log(f"❌ 大鱼号账号 {account} 手机号登录失败")
                self.parent.after(0, lambda: messagebox.showerror("登录失败", f"大鱼号账号 {account} 手机号登录失败"))

                # 如果浏览器仍然活动，提示用户可以手动关闭
                if driver:
                    self.log(f"浏览器仍然处于打开状态，您可以随时手动关闭")
                    self.parent.after(0, lambda: messagebox.showinfo("提示", "浏览器仍然处于打开状态，您可以随时手动关闭。"))

        except Exception as e:
            self.log(f"❌ 大鱼号账号 {account} 手机号登录过程中发生错误: {str(e)}")
            self.parent.after(0, lambda: messagebox.showerror("错误", f"大鱼号账号 {account} 手机号登录过程中发生错误: {str(e)}"))
            import traceback
            traceback.print_exc()

    def _ask_for_dayu_cookie_update(self, account, cookies, cookie_path, driver):
        """询问是否更新大鱼号Cookie"""
        from tkinter import messagebox

        if messagebox.askyesno("更新Cookie", f"大鱼号账号 {account} 手机号登录成功！\n\n是否将新的Cookie保存到文件中？\n\n这将覆盖现有的Cookie文件。"):
            try:
                # 保存Cookie到文件
                import json
                with open(cookie_path, 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, ensure_ascii=False, indent=2)

                self.log(f"✅ 大鱼号账号 {account} Cookie已更新保存")
                messagebox.showinfo("保存成功", f"大鱼号账号 {account} 的Cookie已成功保存到文件中。")

            except Exception as e:
                self.log(f"❌ 保存Cookie失败: {str(e)}")
                messagebox.showerror("保存失败", f"保存Cookie时发生错误: {str(e)}")
        else:
            self.log(f"跳过Cookie保存")

        # 关闭浏览器
        if driver:
            try:
                driver.quit()
            except:
                pass

    def _ask_for_netease_phone_login(self, account, netease_login, cookie_path):
        """询问是否尝试网易号手机号登录"""
        from tkinter import messagebox

        if not messagebox.askyesno("登录失败", f"网易号账号 {account} Cookie登录失败，可能Cookie已过期。\n\n是否尝试使用手机号登录？"):
            messagebox.showerror("登录失败", f"网易号账号 {account} 登录失败")
            return

        self.log(f"正在尝试使用手机号登录网易号账号: {account}")

        # 显示提示对话框
        messagebox.showinfo("操作提示", "请在打开的浏览器中完成网易号账号登录操作。\n\n登录地址：https://mp.163.com/\n\n浏览器将保持打开状态，您可以随时手动关闭。")

        # 在后台线程中执行手机号登录
        import threading
        threading.Thread(target=self._netease_phone_login_thread, args=(account, netease_login, cookie_path), daemon=True).start()

    def _netease_phone_login_thread(self, account, netease_login, cookie_path):
        """在后台线程中执行网易号手机号登录"""
        try:
            # 使用手机号登录
            login_success, driver, cookies = netease_login.login_with_phone(False)

            if login_success and cookies:
                self.log(f"✅ 网易号账号 {account} 手机号登录成功")

                # 在主线程中询问是否更新Cookie
                self.parent.after(0, lambda: self._ask_for_netease_cookie_update(account, cookies, cookie_path, driver))
            else:
                self.log(f"❌ 网易号账号 {account} 手机号登录失败")
                self.parent.after(0, lambda: messagebox.showerror("登录失败", f"网易号账号 {account} 手机号登录失败"))

                # 如果浏览器仍然活动，提示用户可以手动关闭
                if driver:
                    self.log(f"浏览器仍然处于打开状态，您可以随时手动关闭")
                    self.parent.after(0, lambda: messagebox.showinfo("提示", "浏览器仍然处于打开状态，您可以随时手动关闭。"))

        except Exception as e:
            self.log(f"❌ 网易号账号 {account} 手机号登录过程中发生错误: {str(e)}")
            self.parent.after(0, lambda: messagebox.showerror("错误", f"网易号账号 {account} 手机号登录过程中发生错误: {str(e)}"))
            import traceback
            traceback.print_exc()

    def _ask_for_netease_cookie_update(self, account, cookies, cookie_path, driver):
        """询问是否更新网易号Cookie（修复：确保手机号登录成功后一定弹窗）"""
        from tkinter import messagebox

        try:
            should_save = messagebox.askyesno(
                "更新Cookie",
                f"网易号账号 {account} 手机号登录成功！\n\n是否将新的Cookie保存到文件中？\n\n这将覆盖现有的Cookie文件。"
            )
        except Exception:
            # 兜底：若因线程问题无法弹窗，直接按保存处理，避免无提示
            should_save = True

        if should_save:
            try:
                # 保存Cookie到文件
                import json
                with open(cookie_path, 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, ensure_ascii=False, indent=2)

                self.log(f"✅ 网易号账号 {account} Cookie已更新保存")
                try:
                    messagebox.showinfo("保存成功", f"网易号账号 {account} 的Cookie已成功保存到文件中。")
                except Exception:
                    pass
            except Exception as e:
                self.log(f"❌ 保存Cookie失败: {str(e)}")
                try:
                    messagebox.showerror("保存失败", f"保存Cookie时发生错误: {str(e)}")
                except Exception:
                    pass
        else:
            self.log(f"跳过Cookie保存")

        # 关闭浏览器
        if driver:
            try:
                driver.quit()
            except:
                pass
