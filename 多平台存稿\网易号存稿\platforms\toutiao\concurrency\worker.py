"""
头条号平台工作线程 - 负责执行具体的存稿任务
基于网易号工作线程，适配头条号平台特殊需求
"""

import os
import time
import queue
import random
import traceback
from typing import Optional, Callable

# 尝试相对导入，如果失败则使用绝对导入
try:
    from ..processor import ToutiaoDraftProcessor
    from ..login import ToutiaoLogin
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)

    from processor import ToutiaoDraftProcessor
    from login import ToutiaoLogin


class ToutiaoWorker:
    """头条号工作线程类，负责执行具体的存稿任务"""

    def __init__(self,
                 account: str,
                 account_dir: str,
                 processed_dir: str,
                 processed_covers_dir: str,
                 video_queue: queue.Queue,
                 archive_completed: bool = True,
                 headless_mode: bool = False,
                 draft_limit: int = 0,
                 loop_limit: int = 0,
                 log_callback: Callable = None,
                 progress_callback: Callable = None,
                 screenshots_dir: str = None):
        """
        初始化头条号工作线程

        Args:
            account: 账号名称
            account_dir: 账号目录
            processed_dir: 已处理视频目录
            processed_covers_dir: 已处理封面目录
            video_queue: 视频队列
            archive_completed: 是否归档已完成视频
            headless_mode: 是否使用无头模式
            draft_limit: 存稿数量限制，0表示不限制
            loop_limit: 循环次数限制，0表示不限制
            log_callback: 日志回调函数
            progress_callback: 进度回调函数
            screenshots_dir: 截图保存目录
        """
        self.account = account
        self.account_dir = account_dir
        self.processed_dir = processed_dir
        self.processed_covers_dir = processed_covers_dir
        self.video_queue = video_queue
        self.archive_completed = archive_completed
        self.headless_mode = headless_mode
        self.draft_limit = draft_limit
        self.loop_limit = loop_limit
        self.log_callback = log_callback
        self.progress_callback = progress_callback
        self.screenshots_dir = screenshots_dir

        # 初始化状态
        self.is_running = False
        self.driver = None
        self.login = None
        self.processor = None
        self.random_video_allocation = True  # 添加缺失的属性

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(f"[头条号-{self.account}] {message}")

    def update_progress(self, progress: int, status: str, details: str) -> None:
        """
        更新进度

        Args:
            progress: 进度百分比
            status: 状态
            details: 详细信息
        """
        if self.progress_callback:
            try:
                self.progress_callback(progress, status, details)
            except Exception as e:
                self.log(f"更新进度时发生异常: {str(e)}")

    def run(self) -> bool:
        """
        运行工作线程

        Returns:
            处理结果
        """
        self.is_running = True
        
        try:
            self.log(f"开始处理头条号账号: {self.account}")
            self.update_progress(0, "初始化", "正在初始化头条号处理器")

            # 1. 初始化头条号登录器
            self.login = ToutiaoLogin(self.log_callback)
            
            # 2. 查找Cookie文件
            cookie_path = self._find_cookie_file()
            if not cookie_path:
                self.log("未找到Cookie文件")
                self.update_progress(0, "失败", "未找到Cookie文件")
                return False

            self.update_progress(10, "登录中", "正在使用Cookie登录")

            # 3. 登录头条号
            success, driver = self.login.login_with_cookies(cookie_path, self.headless_mode, self.account)
            if not success:
                self.log("登录失败")
                self.update_progress(0, "失败", "登录失败")
                return False

            self.driver = driver
            self.update_progress(20, "已登录", "登录成功，准备处理视频")

            # 4. 创建头条号处理器
            self.processor = ToutiaoDraftProcessor(
                account_dir=self.account_dir,
                processed_dir=self.processed_dir,
                processed_covers_dir=self.processed_covers_dir,
                archive_completed=self.archive_completed,
                headless_mode=self.headless_mode,
                draft_limit=self.draft_limit,
                loop_limit=self.loop_limit,
                log_callback=self.log_callback,
                screenshots_dir=self.screenshots_dir,
                random_video_allocation=self.random_video_allocation
            )

            # 设置处理器的driver，避免重复创建
            self.processor.driver = self.driver

            # 5. 处理视频
            self.update_progress(30, "处理中", "开始处理视频")
            
            # 初始化计数器
            draft_count = 0
            loop_count = 0

            # 读取共享队列中的视频逐个处理（与并发管理器一致）
            try:
                while self.is_running:
                    # 检查存稿数量限制
                    if self.draft_limit > 0 and draft_count >= self.draft_limit:
                        self.log(f"已达到存稿数量限制: {self.draft_limit} 个视频")
                        break

                    # 从共享队列获取一个视频任务
                    try:
                        video_path = self.video_queue.get(timeout=5.0)
                    except queue.Empty:
                        self.log("视频队列为空，处理完成")
                        break

                    # 处理单个视频
                    self.log(f"开始处理视频: {os.path.basename(video_path)}")

                    # 获取对应的封面文件（通过处理器以统一逻辑）
                    cover_path = self.processor.get_cover_file(video_path)

                    # 使用处理器处理视频
                    success = self.processor._process_single_video(video_path, cover_path)

                    if success:
                        draft_count += 1
                        self.log(f"✅ 视频存稿成功: {os.path.basename(video_path)}")

                        # 归档已完成的视频
                        if self.archive_completed:
                            self.processor.archive_video(video_path, cover_path, success=True)
                    else:
                        self.log(f"❌ 视频存稿失败: {os.path.basename(video_path)}")

                        # 归档失败的视频
                        if self.archive_completed:
                            self.processor.archive_video(video_path, cover_path, success=False)

                    # 更新进度
                    self.update_progress(min(90, 30 + draft_count * 5), "处理中", f"已处理 {draft_count} 个视频")

                    # 标记队列任务完成
                    self.video_queue.task_done()

                    # 随机等待，避免频繁操作
                    time.sleep(random.uniform(1.5, 3.5))

            except Exception as e:
                self.log(f"处理视频时发生异常: {str(e)}")
                traceback.print_exc()
                return False

            # 处理完成
            self.update_progress(100, "完成", f"处理完成，成功存稿 {draft_count} 个视频")
            self.log(f"头条号账号 {self.account} 处理完成，成功存稿 {draft_count} 个视频")
            
            return True

        except Exception as e:
            error_msg = f"处理过程中发生异常: {str(e)}"
            self.log(f"❌ {error_msg}")
            self.update_progress(0, "异常", error_msg)
            traceback.print_exc()
            return False

        finally:
            # 清理资源
            self._cleanup()
            self.is_running = False

    def _find_cookie_file(self) -> Optional[str]:
        """
        查找账号的Cookie文件

        优先兼容处理器保存的账号名.txt（JSON内容），再兼容历史.json命名。

        Returns:
            Cookie文件路径，如果未找到返回None
        """
        # 1) 首选：账号名.txt（processor.process_account 会以 JSON 写入 .txt）
        primary = os.path.join(self.account_dir, f"{self.account}.txt")
        if os.path.exists(primary):
            return primary

        # 2) 兼容：若存在早期/其他命名的 .json 文件，则使用之
        fallback_patterns = [
            f"{self.account}.json",
            f"{self.account}_cookies.json",
            f"toutiao_{self.account}.json",
            f"头条_{self.account}.json"
        ]
        for name in fallback_patterns:
            path = os.path.join(self.account_dir, name)
            if os.path.exists(path):
                return path

        # 3) 未找到
        return None

    def _get_cover_file(self, video_path: str) -> Optional[str]:
        """
        获取视频对应的封面文件

        Args:
            video_path: 视频文件路径

        Returns:
            封面文件路径，如果未找到返回None
        """
        # 获取视频文件名（不含扩展名）
        video_name = os.path.splitext(os.path.basename(video_path))[0]

        # 查找匹配的封面文件
        for ext in ['.jpg', '.jpeg', '.png', '.webp']:
            cover_path = os.path.join(self.processed_covers_dir, f"{video_name}{ext}")
            if os.path.exists(cover_path):
                # 在并发环境中，创建封面文件的副本，避免多个线程同时访问同一个文件
                try:
                    # 创建临时目录（如果不存在）
                    temp_dir = os.path.join(self.processed_covers_dir, "temp")
                    os.makedirs(temp_dir, exist_ok=True)

                    # 创建封面文件的副本
                    import shutil
                    temp_cover_path = os.path.join(temp_dir, f"{video_name}_{int(time.time())}_{random.randint(1000, 9999)}{ext}")
                    shutil.copy2(cover_path, temp_cover_path)

                    self.log(f"已创建封面文件副本: {os.path.basename(temp_cover_path)}")
                    return temp_cover_path
                except Exception as copy_error:
                    self.log(f"创建封面文件副本失败: {str(copy_error)}")
                    # 如果创建副本失败，仍然返回原始路径
                    return cover_path

        return None

    def _archive_video(self, video_path: str, cover_path: Optional[str] = None) -> None:
        """
        归档已完成的视频

        Args:
            video_path: 视频文件路径
            cover_path: 封面文件路径
        """
        try:
            # 创建归档目录
            archive_dir = os.path.join(os.path.dirname(self.processed_dir), "已完成")
            os.makedirs(archive_dir, exist_ok=True)

            # 移动视频文件
            import shutil
            video_name = os.path.basename(video_path)
            archive_video_path = os.path.join(archive_dir, video_name)
            shutil.move(video_path, archive_video_path)

            # 移动封面文件（如果存在）
            if cover_path and os.path.exists(cover_path):
                cover_name = os.path.basename(cover_path)
                archive_cover_path = os.path.join(archive_dir, cover_name)
                shutil.move(cover_path, archive_cover_path)

            self.log(f"已归档视频: {video_name}")

        except Exception as e:
            self.log(f"归档视频失败: {str(e)}")

    def _cleanup(self) -> None:
        """清理资源"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
        except Exception as e:
            self.log(f"清理浏览器资源时发生异常: {str(e)}")

    def stop(self) -> None:
        """停止工作线程"""
        self.is_running = False
        self.log("头条号工作线程已停止")
