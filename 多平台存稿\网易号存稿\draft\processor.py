"""
存稿处理器模块 - 负责处理存稿任务
"""

import os
import time
import random
import traceback
import queue
import json
import hashlib
from datetime import datetime
from typing import Optional, Callable, List, Dict, Any
# 导入 selenium 相关模块
from selenium import webdriver
from selenium.webdriver.common.by import By

from 网易号存稿.browser.driver import DriverManager
from 网易号存稿.account.login import AccountLogin
from 网易号存稿.common.constants import NETEASE_PUBLISH_URL


class VideoAllocationTracker:
    """视频分配跟踪器 - 负责跟踪每个账号处理过的视频，实现真正的视频分配机制"""

    def __init__(self, base_dir: str, log_callback: Callable = None):
        """
        初始化视频分配跟踪器

        Args:
            base_dir: 基础目录，用于存储分配跟踪数据
            log_callback: 日志回调函数
        """
        self.base_dir = base_dir
        self.log_callback = log_callback

        # 分配跟踪数据文件路径
        self.allocation_db_path = os.path.join(base_dir, "video_allocation_tracker.json")

        # 账号处理记录目录
        self.account_records_dir = os.path.join(base_dir, "account_video_records")
        os.makedirs(self.account_records_dir, exist_ok=True)

        # 加载分配数据
        self.allocation_data = self._load_allocation_data()

    def log(self, message: str) -> None:
        """记录日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def _load_allocation_data(self) -> Dict[str, Any]:
        """加载视频分配数据"""
        try:
            if os.path.exists(self.allocation_db_path):
                with open(self.allocation_db_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {
                    "videos": {},  # 视频ID -> {accounts: [账号列表], created_time: 创建时间}
                    "accounts": {},  # 账号 -> {processed_videos: [视频ID列表], last_update: 最后更新时间}
                    "statistics": {  # 统计信息
                        "total_videos": 0,
                        "total_allocations": 0,
                        "last_update": datetime.now().isoformat()
                    }
                }
        except Exception as e:
            self.log(f"加载视频分配数据失败: {str(e)}")
            return {
                "videos": {},
                "accounts": {},
                "statistics": {
                    "total_videos": 0,
                    "total_allocations": 0,
                    "last_update": datetime.now().isoformat()
                }
            }

    def _save_allocation_data(self) -> None:
        """保存视频分配数据"""
        try:
            # 更新统计信息
            self.allocation_data["statistics"]["last_update"] = datetime.now().isoformat()

            with open(self.allocation_db_path, 'w', encoding='utf-8') as f:
                json.dump(self.allocation_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log(f"保存视频分配数据失败: {str(e)}")

    def _get_video_id(self, video_path: str) -> str:
        """
        生成视频的唯一标识符

        Args:
            video_path: 视频文件路径

        Returns:
            视频唯一标识符
        """
        # 使用文件名（不含扩展名）和文件大小生成唯一ID
        try:
            filename = os.path.splitext(os.path.basename(video_path))[0]
            file_size = os.path.getsize(video_path) if os.path.exists(video_path) else 0

            # 创建基于文件名和大小的哈希
            content = f"{filename}_{file_size}"
            video_id = hashlib.md5(content.encode('utf-8')).hexdigest()[:16]

            return video_id
        except Exception as e:
            self.log(f"生成视频ID失败: {str(e)}")
            # 如果出错，使用文件名作为ID
            return os.path.splitext(os.path.basename(video_path))[0][:16]

    def get_available_videos_for_account(self, account: str, video_files: List[str]) -> List[str]:
        """
        为指定账号获取可用的视频文件列表（该账号未处理过的视频）

        Args:
            account: 账号名称
            video_files: 所有可用的视频文件列表

        Returns:
            该账号可以处理的视频文件列表
        """
        try:
            # 获取该账号已处理的视频ID列表
            account_data = self.allocation_data["accounts"].get(account, {})
            processed_video_ids = set(account_data.get("processed_videos", []))

            # 筛选出该账号未处理过的视频
            available_videos = []
            unprocessed_videos = []  # 完全未被任何账号处理过的视频
            processed_by_others = []  # 被其他账号处理过但当前账号未处理的视频

            for video_path in video_files:
                if not os.path.exists(video_path):
                    continue

                video_id = self._get_video_id(video_path)

                # 如果当前账号未处理过这个视频
                if video_id not in processed_video_ids:
                    available_videos.append(video_path)

                    # 检查是否被其他账号处理过
                    video_data = self.allocation_data["videos"].get(video_id, {})
                    if video_data.get("accounts", []):
                        processed_by_others.append(video_path)
                    else:
                        unprocessed_videos.append(video_path)

            # 优先分配完全未被处理的视频，然后是被其他账号处理过的视频
            prioritized_videos = unprocessed_videos + processed_by_others

            # 简化日志输出，只显示总数
            self.log(f"为账号 {account} 分配了 {len(available_videos)} 个可用视频")

            return prioritized_videos

        except Exception as e:
            self.log(f"获取账号可用视频失败: {str(e)}")
            return video_files  # 如果出错，返回所有视频

    def mark_video_processed(self, account: str, video_path: str, success: bool = True) -> None:
        """
        标记视频已被指定账号处理

        Args:
            account: 账号名称
            video_path: 视频文件路径
            success: 是否处理成功
        """
        try:
            video_id = self._get_video_id(video_path)
            current_time = datetime.now().isoformat()

            # 更新视频数据
            if video_id not in self.allocation_data["videos"]:
                self.allocation_data["videos"][video_id] = {
                    "accounts": [],
                    "created_time": current_time,
                    "file_path": video_path,
                    "file_name": os.path.basename(video_path)
                }
                self.allocation_data["statistics"]["total_videos"] += 1

            # 添加账号到视频的处理记录中（如果还没有）
            video_data = self.allocation_data["videos"][video_id]
            if account not in video_data["accounts"]:
                video_data["accounts"].append(account)
                video_data["last_processed"] = current_time
                self.allocation_data["statistics"]["total_allocations"] += 1

            # 更新账号数据
            if account not in self.allocation_data["accounts"]:
                self.allocation_data["accounts"][account] = {
                    "processed_videos": [],
                    "created_time": current_time,
                    "total_processed": 0,
                    "total_success": 0,
                    "total_failed": 0
                }

            account_data = self.allocation_data["accounts"][account]
            if video_id not in account_data["processed_videos"]:
                account_data["processed_videos"].append(video_id)
                account_data["total_processed"] += 1

            if success:
                account_data["total_success"] += 1
            else:
                account_data["total_failed"] += 1

            account_data["last_update"] = current_time

            # 保存到账号专属记录文件
            self._save_account_record(account, video_id, video_path, success, current_time)

            # 保存分配数据
            self._save_allocation_data()

            self.log(f"已标记视频 {os.path.basename(video_path)} 被账号 {account} 处理 ({'成功' if success else '失败'})")

        except Exception as e:
            self.log(f"标记视频处理状态失败: {str(e)}")

    def _save_account_record(self, account: str, video_id: str, video_path: str, success: bool, timestamp: str) -> None:
        """保存账号专属的处理记录"""
        try:
            record_file = os.path.join(self.account_records_dir, f"{account}_records.json")

            # 加载现有记录
            records = []
            if os.path.exists(record_file):
                with open(record_file, 'r', encoding='utf-8') as f:
                    records = json.load(f)

            # 添加新记录
            new_record = {
                "video_id": video_id,
                "video_path": video_path,
                "video_name": os.path.basename(video_path),
                "success": success,
                "timestamp": timestamp,
                "status": "成功" if success else "失败"
            }

            records.append(new_record)

            # 保存记录（保留最近1000条记录）
            if len(records) > 1000:
                records = records[-1000:]

            with open(record_file, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.log(f"保存账号记录失败: {str(e)}")

    def get_allocation_statistics(self) -> Dict[str, Any]:
        """获取分配统计信息"""
        try:
            stats = self.allocation_data["statistics"].copy()

            # 计算额外统计信息
            total_accounts = len(self.allocation_data["accounts"])
            total_videos = len(self.allocation_data["videos"])

            # 计算平均分配率
            if total_accounts > 0 and total_videos > 0:
                avg_allocation_rate = self.allocation_data["statistics"]["total_allocations"] / (total_accounts * total_videos)
            else:
                avg_allocation_rate = 0

            stats.update({
                "total_accounts": total_accounts,
                "total_unique_videos": total_videos,
                "average_allocation_rate": round(avg_allocation_rate * 100, 2)  # 转换为百分比
            })

            return stats

        except Exception as e:
            self.log(f"获取统计信息失败: {str(e)}")
            return {}

    def reset_allocation_data(self) -> None:
        """重置所有分配数据"""
        try:
            self.allocation_data = {
                "videos": {},
                "accounts": {},
                "statistics": {
                    "total_videos": 0,
                    "total_allocations": 0,
                    "last_update": datetime.now().isoformat()
                }
            }

            # 删除所有账号记录文件
            if os.path.exists(self.account_records_dir):
                for file in os.listdir(self.account_records_dir):
                    if file.endswith("_records.json"):
                        os.remove(os.path.join(self.account_records_dir, file))

            self._save_allocation_data()
            self.log("已重置所有视频分配数据")

        except Exception as e:
            self.log(f"重置分配数据失败: {str(e)}")

    def get_account_processed_videos(self, account: str) -> List[Dict[str, Any]]:
        """获取指定账号的处理记录"""
        try:
            record_file = os.path.join(self.account_records_dir, f"{account}_records.json")

            if os.path.exists(record_file):
                with open(record_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return []

        except Exception as e:
            self.log(f"获取账号处理记录失败: {str(e)}")
            return []

class DraftProcessor:
    """存稿处理器类 - 负责处理存稿任务"""

    # 平台标识符 - 用于模块识别和配置加载
    PLATFORM_ID = "netease"
    PLATFORM_NAME = "网易号平台"

    def __init__(self,
                 account_dir: str,
                 processed_dir: str,
                 processed_covers_dir: str,
                 archive_completed: bool = True,
                 headless_mode: bool = False,
                 draft_limit: int = 0,
                 loop_limit: int = 0,
                 log_callback: Callable = None,
                 screenshots_dir: str = None,
                 random_video_allocation: bool = True,
                 update_draft_detail_callback: Callable = None):
        """
        初始化存稿处理器

        Args:
            account_dir: 账号目录
            processed_dir: 已处理视频目录
            processed_covers_dir: 已处理封面目录
            archive_completed: 是否归档已完成视频
            headless_mode: 是否使用无头模式
            draft_limit: 存稿数量限制，0表示不限制
            loop_limit: 循环次数限制，0表示不限制
            log_callback: 日志回调函数
            screenshots_dir: 截图保存目录
            random_video_allocation: 是否随机分配视频，True为随机分配，False为顺序分配
            update_draft_detail_callback: 更新存稿详情数据的回调函数，接收参数(account, video_path, status, reason, screenshot)
        """
        self.account_dir = account_dir
        self.processed_dir = processed_dir
        self.processed_covers_dir = processed_covers_dir
        self.archive_completed = archive_completed
        self.headless_mode = headless_mode
        self.draft_limit = draft_limit
        self.loop_limit = loop_limit
        self.log_callback = log_callback
        self.screenshots_dir = screenshots_dir
        self.random_video_allocation = random_video_allocation
        self.update_draft_detail_callback = update_draft_detail_callback
        self.is_running = True  # 初始化为True，确保循环能够开始
        self.driver_manager = None
        self.account_login = None
        self.driver = None
        self.video_queue = queue.Queue()
        self.total_draft_count = 0  # 初始化总存稿成功数量

        # 设置平台标识
        self.platform_id = self.PLATFORM_ID
        self.platform_name = self.PLATFORM_NAME

        # 初始化视频分配跟踪器
        try:
            # 使用processed_dir的父目录作为跟踪数据存储目录
            tracker_base_dir = os.path.dirname(processed_dir) if processed_dir else account_dir
            self.video_tracker = VideoAllocationTracker(tracker_base_dir, log_callback)
            self.log("✅ 视频分配跟踪器初始化成功", internal=True)
        except Exception as e:
            self.log(f"⚠️ 视频分配跟踪器初始化失败: {str(e)}", internal=True)
            self.video_tracker = None

    def log(self, message: str, internal: bool = False) -> None:
        """
        记录日志

        Args:
            message: 日志消息
            internal: 是否为内部日志（内部日志会添加平台标记，但不显示给用户）
        """
        if internal:
            # 内部日志添加平台标记，用于调试和模块识别
            formatted_message = f"[{self.platform_id}] {message}"
        else:
            # 用户日志不显示平台标记
            formatted_message = message

        if self.log_callback:
            self.log_callback(formatted_message)
        else:
            print(formatted_message)

    def start(self, account: str = None, accounts: List[str] = None):
        """
        开始存稿任务

        Args:
            account: 单个账号名称
            accounts: 多个账号名称列表

        Returns:
            元组: (是否成功启动, 处理过的视频信息列表)
        """
        # 确保is_running为True
        self.is_running = True
        self.log("开始存稿任务，设置运行状态为True")

        # 初始化处理过的视频信息列表
        processed_videos = []

        try:
            # 确定要处理的账号
            accounts_to_process = []
            if account:
                accounts_to_process = [account]
            elif accounts:
                accounts_to_process = accounts

            if not accounts_to_process:
                self.log("没有指定要处理的账号")
                self.is_running = False
                return False, processed_videos

            # 初始化驱动管理器
            self.driver_manager = DriverManager(self.log_callback)

            # 初始化账号登录
            self.account_login = AccountLogin(self.driver_manager, self.log_callback)

            # 处理账号
            for account in accounts_to_process:
                if not self.is_running:
                    self.log("任务已被停止，中断处理")
                    break

                self.log(f"开始处理账号: {account}")

                # 处理单个账号
                success, account_videos = self.process_account(account)

                # 添加到处理过的视频信息列表
                processed_videos.extend(account_videos)

                if success:
                    self.log(f"账号 {account} 处理完成")
                else:
                    self.log(f"账号 {account} 处理失败")

            self.log("所有账号处理完成")
            return True, processed_videos

        except Exception as e:
            self.log(f"存稿任务发生错误: {str(e)}")
            traceback.print_exc()
            return False, processed_videos

        finally:
            self.log("存稿任务结束，设置运行状态为False")
            self.is_running = False
            # 关闭浏览器
            if self.driver_manager:
                self.driver_manager.close_driver()

            # 显示视频分配统计信息
            if self.video_tracker:
                try:
                    stats = self.video_tracker.get_allocation_statistics()
                    if stats:
                        self.log("📊 视频分配统计信息:")
                        self.log(f"  - 总账号数: {stats.get('total_accounts', 0)}")
                        self.log(f"  - 总视频数: {stats.get('total_unique_videos', 0)}")
                        self.log(f"  - 总分配次数: {stats.get('total_allocations', 0)}")
                        self.log(f"  - 平均分配率: {stats.get('average_allocation_rate', 0)}%")
                except Exception as e:
                    self.log(f"获取分配统计信息失败: {str(e)}", internal=True)

    def stop(self) -> None:
        """停止存稿任务"""
        if not self.is_running:
            return

        self.is_running = False
        self.log("正在停止任务...")

        # 关闭浏览器
        if self.driver_manager:
            self.driver_manager.close_driver()

    def truncate_filename(self, filename):
        """截取文件名，用于去重"""
        # 获取文件名（不含扩展名）
        name, ext = os.path.splitext(filename)
        # 截取前30个字符作为唯一标识
        return name[:30] + ext

    def prepare_unique_files(self, video_dir, account: str = None):
        """准备唯一的视频文件列表，处理重复文件，并使用视频分配跟踪器"""
        video_files = []
        seen_files = set()
        duplicate_count = 0

        # 视频文件扩展名
        VIDEO_EXTENSIONS = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']

        # 确保目录存在
        if not os.path.exists(video_dir):
            self.log(f"警告: 视频目录不存在: {video_dir}")
            try:
                os.makedirs(video_dir, exist_ok=True)
                self.log(f"已创建视频目录: {video_dir}")
            except Exception as e:
                self.log(f"创建视频目录失败: {str(e)}")
                return []

        # 遍历目录中的文件（简化日志输出）
        try:
            files = os.listdir(video_dir)

            for file in files:
                file_path = os.path.join(video_dir, file)

                # 检查是否为文件
                if not os.path.isfile(file_path):
                    continue

                # 检查是否为视频文件
                if any(file.lower().endswith(ext) for ext in VIDEO_EXTENSIONS):
                    # 不再记录每个找到的视频文件，减少日志量
                    truncated_name = self.truncate_filename(file)
                    if truncated_name in seen_files:
                        # 只增加计数，不再打印每个重复文件的详情
                        duplicate_count += 1
                        continue
                    seen_files.add(truncated_name)
                    video_files.append(file_path)
        except Exception as e:
            self.log(f"遍历视频目录时出错: {str(e)}")
            traceback.print_exc()
            return []

        # 打印重复文件总数
        if duplicate_count > 0:
            self.log(f"注意: 发现 {duplicate_count} 个重复视频文件，将被跳过")

        # 检查是否有可用文件
        if not video_files:
            self.log(f"警告: 视频文件夹中没有可用的视频文件")
            return []

        # 如果提供了账号信息且视频分配跟踪器可用，使用智能分配
        if account and self.video_tracker:
            allocated_videos = self.video_tracker.get_available_videos_for_account(account, video_files)

            if allocated_videos:
                # 简化日志输出，在VideoAllocationTracker中已有日志
                return allocated_videos
            else:
                self.log(f"❌ 没有为账号 {account} 找到可分配的视频")
                return []
        else:
            # 传统模式：简化日志输出
            self.log(f"📁 找到 {len(video_files)} 个视频文件")
            return video_files

    def process_account(self, account: str):
        """
        处理单个账号

        Args:
            account: 账号名称

        Returns:
            元组: (是否成功处理, 处理过的视频信息列表)
        """
        # 初始化处理过的视频信息列表
        processed_videos = []

        try:
            # 检查目录设置
            if not self.processed_dir or not os.path.exists(self.processed_dir):
                self.log(f"待存稿视频目录不存在或未设置: {self.processed_dir}")
                try:
                    if self.processed_dir:
                        os.makedirs(self.processed_dir, exist_ok=True)
                        self.log(f"已创建待存稿视频目录: {self.processed_dir}")
                except Exception as e:
                    self.log(f"创建待存稿视频目录失败: {str(e)}")
                    return False, processed_videos

            # 获取账号Cookie路径 - 使用与查询功能一致的多格式查找逻辑
            cookie_path = self._find_cookie_file(account)
            if not cookie_path:
                self.log(f"❌ 头条账号 {account} Cookie文件不存在")
                return False, processed_videos

            # 确保驱动管理器和账号登录对象已初始化
            if self.driver_manager is None:
                # 静默初始化驱动管理器，不输出日志
                self.driver_manager = DriverManager(self.log_callback)

            if self.account_login is None:
                # 静默初始化账号登录对象，不输出日志
                self.account_login = AccountLogin(self.driver_manager, self.log_callback)

            # 静默执行登录过程，在浏览器启动时一起输出日志 - 传递账号目录
            retry_times = 0
            max_retry = 0
            try:
                # 从UI配置读取重试次数与手机号回退
                if hasattr(self, 'log_callback') and hasattr(self, 'platform_name'):
                    # 尝试通过回调上下文获取UI（不强依赖）
                    pass
                max_retry = getattr(self, 'login_retry_times', 0)
            except Exception:
                max_retry = 0

            while True:
                login_success, self.driver = self.account_login.login_with_cookies(cookie_path, self.headless_mode, account)
                if login_success and self.driver:
                    break
                if retry_times >= max_retry:
                    break
                retry_times += 1
                self.log(f"⚠️ Cookie登录失败，准备第 {retry_times}/{max_retry} 次重试: {account}")
                time.sleep(1)

            if not login_success or not self.driver:
                self.log(f"登录账号失败: {account}")

                # 记录登录失败信息到存稿详情
                if hasattr(self, 'update_draft_detail_callback') and self.update_draft_detail_callback:
                    # 尝试获取登录失败截图
                    screenshot_path = ""

                    # 首先尝试从account_login获取截图路径
                    if hasattr(self.account_login, 'last_screenshot') and self.account_login.last_screenshot:
                        screenshot_path = self.account_login.last_screenshot
                        self.log(f"从account_login获取到登录失败截图: {screenshot_path}")

                    # 如果没有获取到截图路径，且驱动还存在，尝试自己截图
                    if not screenshot_path and self.driver:
                        try:
                            # 获取截图目录
                            if hasattr(self, 'screenshots_dir') and self.screenshots_dir:
                                screenshots_dir = self.screenshots_dir
                            else:
                                screenshots_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "screenshots")

                            # 创建截图目录
                            os.makedirs(screenshots_dir, exist_ok=True)

                            # 生成截图文件名
                            timestamp = time.strftime("%Y%m%d%H%M%S")
                            screenshot_filename = f"{account}_login_failed_{timestamp}.png"
                            screenshot_path = os.path.join(screenshots_dir, screenshot_filename)

                            # 保存截图
                            self.driver.save_screenshot(screenshot_path)
                            self.log(f"已保存登录失败截图: {screenshot_path}")

                            # 确保截图路径是绝对路径
                            if not os.path.isabs(screenshot_path):
                                screenshot_path = os.path.abspath(screenshot_path)
                        except Exception as e:
                            self.log(f"保存登录失败截图时出错: {str(e)}")
                            screenshot_path = ""

                    # 更新存稿详情，使用空字符串作为视频路径，表示这是账号级别的错误
                    # 创建包含存稿成功数量的视频信息（登录失败情况下为0）
                    video_info = {
                        "successful_drafts": 0,
                        "video_path": "",
                        "status": "登录失败"
                    }
                    self.update_draft_detail_callback(account, "", "失败", "登录失败，Cookie可能已过期", screenshot_path, video_info)
                    self.log(f"已更新存稿详情数据: 账号 {account} - 登录失败")

                return False, processed_videos

            self.log(f"✅ 账号 {account} 登录成功")
            self.log("开始存稿任务")
            self.driver.get(NETEASE_PUBLISH_URL)
            self.log("正在打开网易视频上传页面")

            # 文件处理循环 - 使用已处理目录作为来源
            processed_dir = self.processed_dir  # 已处理视频目录

            # 初始化计数器
            draft_count = 0  # 当前循环的存稿数量
            total_draft_count = 0  # 所有循环的总存稿数量
            loop_count = 0   # 已循环次数

            # 获取限制值
            draft_limit = self.draft_limit  # 存稿数量限制
            loop_limit = self.loop_limit    # 循环次数限制

            # 显示限制设置（合并日志输出）
            if draft_limit > 0 or loop_limit > 0:
                settings = []
                if draft_limit > 0:
                    settings.append(f"{draft_limit}个视频")
                if loop_limit > 0:
                    settings.append(f"{loop_limit}次循环")
                self.log(f"⚙️ 任务配置: {'/'.join(settings)}")

            # 获取视频文件列表 - 使用prepare_unique_files方法，传入账号信息进行智能分配
            video_files = self.prepare_unique_files(processed_dir, account)

            if not video_files:
                self.log("没有找到可用的视频文件")
                return False, processed_videos

            # 根据配置决定是否随机打乱视频文件顺序（合并日志输出）
            if self.random_video_allocation:
                random.shuffle(video_files)
                self.log(f"📁 文件准备完成: {len(video_files)}个视频已随机排序")
            else:
                video_files.sort()
                self.log(f"📁 文件准备完成: {len(video_files)}个视频已排序")

            # 循环处理视频文件（简化日志输出）
            while self.is_running:
                # 增加循环计数
                loop_count += 1

                # 显示当前循环次数（简化格式）
                if loop_limit > 0:
                    self.log(f"🔄 开始第{loop_count}/{loop_limit}轮处理")
                else:
                    self.log(f"🔄 开始第{loop_count}轮处理")

                # 每次循环开始时重置当前循环的存稿计数（如果启用了存稿限制）
                if draft_limit > 0:
                    draft_count = 0  # 只重置当前循环的计数，不影响总计数

                # 如果是新的循环且不是第一次循环，重新访问上传页面
                if loop_count > 1:
                    self.driver.get(NETEASE_PUBLISH_URL)
                    time.sleep(2)
                    self.log("重新打开网易视频上传页面")

                # 在第一次循环时不检查循环次数限制，确保至少执行一次完整的循环
                # 这个检查移到了循环结束时

                # 从已处理目录获取视频文件（简化日志输出）
                video_files = self.prepare_unique_files(processed_dir, account)

                if not video_files:
                    self.log("没有找到待存稿的视频，等待10秒后重试...")
                    time.sleep(10)
                    continue  # 如果没有视频，继续下一次循环
                else:
                    # 根据配置决定是否随机打乱视频文件顺序（合并日志输出）
                    if self.random_video_allocation:
                        random.shuffle(video_files)
                        self.log(f"📁 循环{loop_count}: {len(video_files)}个视频已随机排序")
                    else:
                        video_files.sort()
                        self.log(f"📁 循环{loop_count}: {len(video_files)}个视频已排序")

                # 只检查文件是否存在，不再打印每个文件的路径
                for video_file in video_files:
                    if not os.path.exists(video_file):
                        self.log(f"警告: 视频文件不存在: {video_file}")

                # 处理视频文件
                current_draft_count = 0  # 当前循环的存稿计数

                for video_path in video_files:
                    # 检查是否达到存稿限制
                    if draft_limit > 0 and current_draft_count >= draft_limit:
                        self.log(f"已达到设定的存稿数量限制 ({draft_limit} 个)，当前循环已完成")
                        break

                    # 检查是否仍在运行
                    if not self.is_running:
                        self.log("任务已停止")
                        break

                    # 循环次数限制已在循环开始时检查，这里不再重复检查

                    # 检查文件是否存在
                    if not os.path.exists(video_path):
                        self.log(f"视频文件不存在，跳过: {video_path}")
                        continue

                    # 查找对应的封面文件（简化日志输出）
                    cover_path = None
                    try:
                        cover_path = self._find_cover_for_video(os.path.basename(video_path))
                        # 只在找到封面时输出简化日志，在_find_cover_for_video中已有详细日志
                    except Exception as e:
                        self.log(f"查找封面文件失败: {str(e)}")
                        # 继续处理，封面不是必须的

                    # 上传视频并存稿
                    self.log(f"开始处理视频: {os.path.basename(video_path)}")
                    self.log(f"视频完整路径: {video_path}")

                    # 使用 OriginalVideoUploader 上传视频并存稿
                    from 网易号存稿.draft.original_uploader import OriginalVideoUploader
                    # 传递正确的日志回调函数，让极简模式能够正常工作
                    uploader = OriginalVideoUploader(self.driver, self.log_callback, self.headless_mode, self.screenshots_dir)

                    # 确保页面已加载到上传页面
                    current_url = self.driver.current_url
                    if "publish" not in current_url:
                        self.log(f"当前不在上传页面，重新导航到上传页面。当前URL: {current_url}")
                        self.driver.get(NETEASE_PUBLISH_URL)
                        time.sleep(3)  # 等待页面加载

                    # 执行存稿
                    self.log("等待上传视频")
                    success = uploader.draft_video(video_path, cover_path)

                    # 根据结果记录关键日志
                    if success:
                        self.log("视频上传成功")
                        self.log("存稿成功")
                    else:
                        # 检查是否是无头模式下的上传失败
                        if self.headless_mode:
                            self.log("无头模式下视频上传失败，正在保存截图...")
                        self.log("❌ 视频上传失败或超时")

                    # 获取失败原因
                    failure_reason = ""
                    screenshot_path = ""

                    if not success:
                        # 尝试从页面获取更详细的错误信息
                        try:
                            # 查找可能包含错误信息的元素
                            error_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '失败') or contains(text(), '错误') or contains(text(), '超过') or contains(@class, 'error')]")
                            for elem in error_elements:
                                if elem.is_displayed():
                                    error_text = elem.text.strip()
                                    if error_text and len(error_text) > 5:  # 确保不是空文本或太短的文本
                                        failure_reason = error_text
                                        self.log(f"检测到错误信息: {failure_reason}")
                                        break
                        except Exception as e:
                            self.log(f"尝试获取错误信息时出错: {str(e)}")

                        # 如果没有找到具体错误信息，使用默认错误信息
                        if not failure_reason:
                            failure_reason = "存稿过程失败，未找到具体原因"

                        # 存稿失败时保存截图
                        try:
                            # 获取截图目录（优先使用配置的目录，如果未配置则使用默认目录）
                            if hasattr(self, 'screenshots_dir') and self.screenshots_dir:
                                screenshots_dir = self.screenshots_dir
                            else:
                                screenshots_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "screenshots")

                            # 创建截图目录
                            os.makedirs(screenshots_dir, exist_ok=True)

                            # 生成截图文件名（使用视频名称和时间戳）
                            video_name = os.path.splitext(os.path.basename(video_path))[0]
                            timestamp = time.strftime("%Y%m%d%H%M%S")
                            screenshot_filename = f"{account}_{video_name}_{timestamp}.png"
                            screenshot_path = os.path.join(screenshots_dir, screenshot_filename)

                            # 保存截图
                            self.driver.save_screenshot(screenshot_path)
                            self.log(f"已保存失败截图: {screenshot_path}")

                            # 确保截图路径是绝对路径
                            if not os.path.isabs(screenshot_path):
                                screenshot_path = os.path.abspath(screenshot_path)

                        except Exception as e:
                            self.log(f"保存截图失败: {str(e)}")
                            screenshot_path = ""

                    # 记录视频处理信息
                    video_info = {
                        "video_path": video_path,
                        "status": "成功" if success else "失败",
                        "reason": "" if success else failure_reason,
                        "screenshot": screenshot_path if not success and screenshot_path else ""
                    }
                    processed_videos.append(video_info)

                    if success:
                        self.log(f"✅ 视频存稿成功: {os.path.basename(video_path)}")
                        draft_count += 1  # 当前循环的存稿数量
                        total_draft_count += 1  # 所有循环的总存稿数量
                        current_draft_count += 1  # 当前循环的存稿计数（用于检查是否达到限制）

                        # 保存总存稿成功数量到实例变量，供外部访问
                        self.total_draft_count = total_draft_count
                        self.log(f"更新总存稿成功数量: {total_draft_count}，当前循环: {loop_count}，循环限制: {loop_limit}，每循环限制: {draft_limit}")

                        # 使用视频分配跟踪器标记视频处理成功
                        if self.video_tracker:
                            self.video_tracker.mark_video_processed(account, video_path, success=True)

                        # 归档已完成的视频
                        if self.archive_completed:
                            self._archive_video(video_path, cover_path)
                            self.log(f"已归档视频: {os.path.basename(video_path)}")

                        # 从视频文件列表中移除已处理的视频，避免重复处理
                        video_files.remove(video_path)
                        self.log(f"已从处理列表中移除视频: {os.path.basename(video_path)}")

                        # 显示存稿进度
                        if draft_limit > 0:
                            self.log(f"当前存稿进度: {current_draft_count}/{draft_limit}")

                        # 立即更新存稿详情数据（成功）
                        if hasattr(self, 'update_draft_detail_callback') and self.update_draft_detail_callback:
                            # 创建包含存稿成功数量的视频信息
                            video_info = {
                                "successful_drafts": total_draft_count,
                                "video_path": video_path,
                                "status": "成功"
                            }
                            self.update_draft_detail_callback(account, video_path, "成功", "", "", video_info)
                            self.log(f"已更新存稿详情数据: {os.path.basename(video_path)} - 成功，总存稿数: {total_draft_count}")
                    else:
                        self.log(f"❌ 视频存稿失败: {os.path.basename(video_path)}")

                        # 使用视频分配跟踪器标记视频处理失败
                        if self.video_tracker:
                            self.video_tracker.mark_video_processed(account, video_path, success=False)

                        # 获取失败原因和截图路径
                        from 网易号存稿.draft.original_uploader import OriginalVideoUploader
                        error_reason = ""
                        screenshot_path = ""

                        # 尝试从uploader获取错误信息
                        if hasattr(uploader, 'last_error') and uploader.last_error:
                            error_reason = uploader.last_error
                            self.log(f"从uploader获取到失败原因: {error_reason}")

                        # 尝试从uploader获取截图路径
                        if hasattr(uploader, 'last_screenshot') and uploader.last_screenshot:
                            screenshot_path = uploader.last_screenshot
                            self.log(f"从uploader获取到截图路径: {screenshot_path}")

                        # 如果没有从uploader获取到错误信息，使用之前获取的错误信息
                        if not error_reason and failure_reason:
                            error_reason = failure_reason
                            self.log(f"使用之前获取的失败原因: {error_reason}")

                        # 如果仍然没有错误信息，使用默认错误信息
                        if not error_reason:
                            error_reason = "存稿失败，未能找到具体原因"
                            self.log(f"使用默认失败原因: {error_reason}")

                        # 立即更新存稿详情数据（失败）
                        if hasattr(self, 'update_draft_detail_callback') and self.update_draft_detail_callback:
                            # 创建包含存稿成功数量的视频信息（失败情况下为0）
                            video_info = {
                                "successful_drafts": total_draft_count,
                                "video_path": video_path,
                                "status": "失败"
                            }
                            self.update_draft_detail_callback(account, video_path, "失败", error_reason, screenshot_path, video_info)
                            self.log(f"已更新存稿详情数据: {os.path.basename(video_path)} - 失败 - {error_reason}")

                        # 从视频文件列表中移除失败的视频，避免重复处理
                        video_files.remove(video_path)
                        self.log(f"已从处理列表中移除失败的视频: {os.path.basename(video_path)}")

                        # 检测到存稿失败后，停止该账号的存稿处理
                        self.log(f"⚠️ 检测到存稿失败，停止账号 {account} 的存稿处理")
                        # 跳出循环，不再处理该账号的其他视频
                        break

                    # 等待一段时间再处理下一个视频
                    wait_time = random.uniform(2.0, 5.0)
                    self.log(f"等待 {wait_time:.1f} 秒后处理下一个视频...")
                    time.sleep(wait_time)

                    # 每次存稿完成后重新打开上传页面准备下一次存稿
                    if current_draft_count < draft_limit and self.is_running and video_files:
                        self.log("重新打开网易视频上传页面")
                        self.driver.get(NETEASE_PUBLISH_URL)
                        time.sleep(2)
                        self.log("✅ 存稿成功后准备下一次存稿")

                # 处理完所有可用视频后，检查循环次数限制
                if loop_limit > 0 and loop_count >= loop_limit:
                    self.log(f"已达到设定的循环次数限制 ({loop_limit} 次)，任务自动停止")
                    break

                # 准备进入下一轮循环
                if draft_limit > 0 and current_draft_count >= draft_limit:
                    # 如果启用了存稿限制且已达到限制，等待一段时间后进入下一轮
                    self.log(f"存稿数量已达到限制 ({draft_limit} 个)，准备进入下一轮循环")
                elif not video_files:
                    self.log("当前没有更多待处理视频，准备进入下一轮循环")
                else:
                    self.log("当前循环完成，准备进入下一轮循环")

                # 等待一段时间后开始下一轮循环
                self.log("等待1秒后开始下一轮循环...")
                time.sleep(1)



            self.log(f"账号 {account} 存稿任务完成，共存稿 {total_draft_count} 个视频")
            return True, processed_videos

        except Exception as e:
            self.log(f"处理账号 {account} 时发生错误: {str(e)}")
            traceback.print_exc()
            # 添加错误信息到处理过的视频列表
            if not processed_videos:
                processed_videos.append({
                    "video_path": "",
                    "status": "失败",
                    "reason": f"处理账号时发生错误: {str(e)}"
                })
            return False, processed_videos

        finally:
            # 确保关闭浏览器
            if hasattr(self, 'driver') and self.driver:
                try:
                    self.log("正在关闭浏览器...")
                    self.driver.quit()
                    self.log("浏览器已关闭")
                except Exception as e:
                    self.log(f"关闭浏览器时出错: {str(e)}")
                self.driver = None

    def _find_cover_for_video(self, video_file: str) -> Optional[str]:
        """
        查找视频对应的封面文件

        Args:
            video_file: 视频文件名

        Returns:
            封面文件路径，如果未找到则返回None
        """
        try:
            # 获取视频文件名（不含扩展名）
            video_name = os.path.splitext(video_file)[0]

            # 常见的封面文件扩展名
            cover_extensions = ['.jpg', '.jpeg', '.png', '.webp']

            # 首先在processed_covers_dir目录中查找匹配的封面文件（已处理封面 - 优先使用）
            if hasattr(self, 'processed_covers_dir') and self.processed_covers_dir:
                for ext in cover_extensions:
                    cover_file = video_name + ext
                    cover_path = os.path.join(self.processed_covers_dir, cover_file)
                    if os.path.exists(cover_path):
                        # 在并发环境中，创建封面文件的副本，避免多个线程同时访问同一个文件
                        try:
                            # 创建临时目录（如果不存在）
                            temp_dir = os.path.join(self.processed_covers_dir, "temp")
                            os.makedirs(temp_dir, exist_ok=True)

                            # 创建封面文件的副本
                            import shutil
                            temp_cover_path = os.path.join(temp_dir, f"{video_name}_{int(time.time())}_{random.randint(1000, 9999)}{ext}")
                            shutil.copy2(cover_path, temp_cover_path)

                            self.log(f"🖼️ 封面准备完成: {os.path.basename(cover_path)}")
                            return temp_cover_path
                        except Exception as copy_error:
                            self.log(f"创建已处理封面文件副本失败: {str(copy_error)}")
                            # 如果创建副本失败，仍然返回原始路径
                            self.log(f"🖼️ 封面准备完成: {os.path.basename(cover_path)}")
                            return cover_path

            # 最后在未处理封面目录中查找匹配的封面文件（作为备用）
            for ext in cover_extensions:
                cover_file = video_name + ext
                # 使用绝对路径查找未处理封面文件
                cover_path = os.path.join(os.path.dirname(self.processed_dir), "封面", cover_file)
                if os.path.exists(cover_path):
                    self.log(f"⚠️ 注意：使用未处理的封面文件: {cover_path}")
                    self.log("建议：将封面文件移动到已处理封面目录中以获得更好的性能")

                    # 在并发环境中，创建封面文件的副本，避免多个线程同时访问同一个文件
                    try:
                        # 创建临时目录（如果不存在）
                        temp_dir = os.path.join(self.processed_covers_dir, "temp") if hasattr(self, 'processed_covers_dir') and self.processed_covers_dir else os.path.join(os.path.dirname(self.processed_dir), "temp")
                        os.makedirs(temp_dir, exist_ok=True)

                        # 创建封面文件的副本
                        import shutil
                        temp_cover_path = os.path.join(temp_dir, f"{video_name}_{int(time.time())}_{random.randint(1000, 9999)}{ext}")
                        shutil.copy2(cover_path, temp_cover_path)

                        self.log(f"已创建未处理封面文件副本: {os.path.basename(temp_cover_path)}")
                        return temp_cover_path
                    except Exception as copy_error:
                        self.log(f"创建未处理封面文件副本失败: {str(copy_error)}")
                        # 如果创建副本失败，仍然返回原始路径
                        self.log(f"在未处理封面目录中找到封面: {cover_path}")
                        return cover_path

            # 未找到封面文件
            self.log(f"未找到视频 {video_file} 对应的封面文件")
            return None

        except Exception as e:
            self.log(f"查找封面文件失败: {str(e)}")
            return None

    def _is_video_file(self, file_path: str) -> bool:
        """
        判断文件是否为视频文件

        Args:
            file_path: 文件路径

        Returns:
            是否为视频文件
        """
        # 视频文件扩展名
        video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']

        # 获取文件扩展名
        _, ext = os.path.splitext(file_path)

        # 判断是否为视频文件
        is_video = ext.lower() in video_extensions

        # 如果是视频文件，打印日志
        if is_video:
            self.log(f"检测到视频文件: {os.path.basename(file_path)}")

        return is_video

    def _archive_video(self, video_path: str, cover_path: Optional[str] = None) -> None:
        """
        归档已完成的视频和封面

        Args:
            video_path: 视频文件路径
            cover_path: 封面文件路径
        """
        try:
            # 创建归档目录
            archive_dir = os.path.join(os.path.dirname(self.processed_dir), "已存稿")
            archive_covers_dir = os.path.join(os.path.dirname(self.processed_dir), "已存稿封面")

            # 确保目录存在
            os.makedirs(archive_dir, exist_ok=True)
            os.makedirs(archive_covers_dir, exist_ok=True)

            # 移动视频文件
            video_filename = os.path.basename(video_path)
            archive_video_path = os.path.join(archive_dir, video_filename)

            # 如果目标文件已存在，添加时间戳
            if os.path.exists(archive_video_path):
                timestamp = time.strftime("%Y%m%d%H%M%S")
                name, ext = os.path.splitext(video_filename)
                archive_video_path = os.path.join(archive_dir, f"{name}_{timestamp}{ext}")

            # 移动视频文件
            os.rename(video_path, archive_video_path)
            self.log(f"已归档视频: {video_filename}")

            # 处理封面文件（如果有）
            if cover_path and os.path.exists(cover_path):
                cover_filename = os.path.basename(cover_path)

                # 检查是否是临时目录中的副本文件
                if "temp" in cover_path:
                    # 如果是临时文件，直接删除，不影响原始封面
                    try:
                        os.remove(cover_path)
                        self.log(f"已删除临时封面副本: {cover_filename}")
                    except Exception as e:
                        self.log(f"删除临时封面副本失败: {str(e)}")

                    # 查找原始封面文件
                    video_name = os.path.splitext(video_filename)[0]
                    original_cover_found = False

                    # 常见的封面文件扩展名
                    cover_extensions = ['.jpg', '.jpeg', '.png', '.webp']

                    # 在processed_covers_dir目录中查找原始封面
                    if hasattr(self, 'processed_covers_dir') and self.processed_covers_dir:
                        for ext in cover_extensions:
                            original_cover_path = os.path.join(self.processed_covers_dir, f"{video_name}{ext}")
                            if os.path.exists(original_cover_path):
                                # 找到原始封面，归档它
                                archive_cover_path = os.path.join(archive_covers_dir, f"{video_name}{ext}")

                                # 如果目标文件已存在，添加时间戳
                                if os.path.exists(archive_cover_path):
                                    timestamp = time.strftime("%Y%m%d%H%M%S")
                                    archive_cover_path = os.path.join(archive_covers_dir, f"{video_name}_{timestamp}{ext}")

                                # 使用复制而不是移动，以防其他线程仍在使用原始文件
                                import shutil
                                shutil.copy2(original_cover_path, archive_cover_path)
                                self.log(f"已归档原始封面: {os.path.basename(original_cover_path)}")

                                # 延迟删除原始封面，给其他线程留出时间
                                time.sleep(2)
                                try:
                                    os.remove(original_cover_path)
                                    self.log(f"已删除原始封面: {os.path.basename(original_cover_path)}")
                                except Exception as e:
                                    self.log(f"删除原始封面失败(可能被其他线程使用): {str(e)}")

                                original_cover_found = True
                                break

                    if not original_cover_found:
                        self.log(f"未找到原始封面文件，无法归档")
                else:
                    # 如果是原始封面文件，正常归档
                    archive_cover_path = os.path.join(archive_covers_dir, cover_filename)

                    # 如果目标文件已存在，添加时间戳
                    if os.path.exists(archive_cover_path):
                        timestamp = time.strftime("%Y%m%d%H%M%S")
                        name, ext = os.path.splitext(cover_filename)
                        archive_cover_path = os.path.join(archive_covers_dir, f"{name}_{timestamp}{ext}")

                    # 使用复制而不是移动，以防其他线程仍在使用原始文件
                    import shutil
                    shutil.copy2(cover_path, archive_cover_path)
                    self.log(f"已归档封面: {cover_filename}")

                    # 延迟删除原始封面，给其他线程留出时间
                    time.sleep(2)
                    try:
                        os.remove(cover_path)
                        self.log(f"已删除原始封面: {cover_filename}")
                    except Exception as e:
                        self.log(f"删除原始封面失败(可能被其他线程使用): {str(e)}")
        except Exception as e:
            self.log(f"归档文件时发生错误: {str(e)}")
            traceback.print_exc()



    def _draft_video(self, video_path: str, cover_path: Optional[str] = None) -> bool:
        """
        上传视频并存稿 - 供并发管理器调用

        Args:
            video_path: 视频文件路径
            cover_path: 封面文件路径

        Returns:
            是否成功存稿
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(video_path):
                self.log(f"视频文件不存在: {video_path}")
                return False

            if cover_path and not os.path.exists(cover_path):
                self.log(f"封面文件不存在: {cover_path}")
                cover_path = None

            # 打开网易视频上传页面
            self.driver.get(NETEASE_PUBLISH_URL)
            self.log("正在打开网易视频上传页面")
            time.sleep(2)  # 等待页面加载

            # 使用 OriginalVideoUploader 上传视频并存稿
            from 网易号存稿.draft.original_uploader import OriginalVideoUploader
            # 传递正确的日志回调函数，让极简模式能够正常工作
            uploader = OriginalVideoUploader(self.driver, self.log_callback, self.headless_mode, self.screenshots_dir)

            # 上传视频并存稿
            success = uploader.draft_video(video_path, cover_path)

            if success:
                self.log(f"视频存稿成功: {os.path.basename(video_path)}")
                return True
            else:
                self.log(f"视频存稿失败: {os.path.basename(video_path)}")
                return False

        except Exception as e:
            self.log(f"存稿过程中发生错误: {str(e)}")
            traceback.print_exc()
            return False

    # ==================== 视频分配跟踪器管理方法 ====================

    def get_video_allocation_statistics(self) -> Dict[str, Any]:
        """
        获取视频分配统计信息

        Returns:
            包含统计信息的字典
        """
        if self.video_tracker:
            return self.video_tracker.get_allocation_statistics()
        else:
            return {}

    def get_account_processed_videos(self, account: str) -> List[Dict[str, Any]]:
        """
        获取指定账号的视频处理记录

        Args:
            account: 账号名称

        Returns:
            账号的视频处理记录列表
        """
        if self.video_tracker:
            return self.video_tracker.get_account_processed_videos(account)
        else:
            return []

    def reset_video_allocation_data(self) -> bool:
        """
        重置所有视频分配数据

        Returns:
            是否成功重置
        """
        try:
            if self.video_tracker:
                self.video_tracker.reset_allocation_data()
                self.log("✅ 已重置所有视频分配数据")
                return True
            else:
                self.log("⚠️ 视频分配跟踪器未初始化，无法重置数据")
                return False
        except Exception as e:
            self.log(f"❌ 重置视频分配数据失败: {str(e)}")
            return False

    def check_video_allocation_status(self, video_path: str) -> Dict[str, Any]:
        """
        检查指定视频的分配状态

        Args:
            video_path: 视频文件路径

        Returns:
            包含视频分配状态的字典
        """
        if not self.video_tracker:
            return {"error": "视频分配跟踪器未初始化"}

        try:
            video_id = self.video_tracker._get_video_id(video_path)
            video_data = self.video_tracker.allocation_data["videos"].get(video_id, {})

            if not video_data:
                return {
                    "video_id": video_id,
                    "video_name": os.path.basename(video_path),
                    "status": "未处理",
                    "processed_accounts": [],
                    "total_processed": 0
                }

            return {
                "video_id": video_id,
                "video_name": os.path.basename(video_path),
                "status": "已处理",
                "processed_accounts": video_data.get("accounts", []),
                "total_processed": len(video_data.get("accounts", [])),
                "created_time": video_data.get("created_time", ""),
                "last_processed": video_data.get("last_processed", "")
            }

        except Exception as e:
            return {"error": f"检查视频分配状态失败: {str(e)}"}

    def get_available_videos_for_account_external(self, account: str, video_dir: str = None) -> List[str]:
        """
        获取指定账号可用的视频列表（外部调用接口）

        Args:
            account: 账号名称
            video_dir: 视频目录，如果为None则使用默认的processed_dir

        Returns:
            可用视频文件路径列表
        """
        if not self.video_tracker:
            self.log("⚠️ 视频分配跟踪器未初始化")
            return []

        try:
            # 使用指定目录或默认目录
            target_dir = video_dir if video_dir else self.processed_dir

            # 直接获取所有视频文件（不使用智能分配，避免递归）
            all_videos = self._get_all_video_files(target_dir)

            # 使用跟踪器获取该账号可用的视频
            available_videos = self.video_tracker.get_available_videos_for_account(account, all_videos)

            return available_videos

        except Exception as e:
            self.log(f"获取账号可用视频失败: {str(e)}")
            return []

    def _get_all_video_files(self, video_dir: str) -> List[str]:
        """
        获取目录中的所有视频文件（内部方法，不使用智能分配）

        Args:
            video_dir: 视频目录

        Returns:
            视频文件路径列表
        """
        video_files = []
        seen_files = set()

        # 视频文件扩展名
        VIDEO_EXTENSIONS = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']

        # 确保目录存在
        if not os.path.exists(video_dir):
            return []

        try:
            files = os.listdir(video_dir)

            for file in files:
                file_path = os.path.join(video_dir, file)

                # 检查是否为文件
                if not os.path.isfile(file_path):
                    continue

                # 检查是否为视频文件
                if any(file.lower().endswith(ext) for ext in VIDEO_EXTENSIONS):
                    truncated_name = self.truncate_filename(file)
                    if truncated_name in seen_files:
                        continue
                    seen_files.add(truncated_name)
                    video_files.append(file_path)

        except Exception as e:
            self.log(f"获取视频文件时出错: {str(e)}")
            return []

        return video_files

    def export_allocation_report(self, output_path: str = None) -> str:
        """
        导出视频分配报告

        Args:
            output_path: 输出文件路径，如果为None则自动生成

        Returns:
            报告文件路径
        """
        if not self.video_tracker:
            self.log("⚠️ 视频分配跟踪器未初始化，无法导出报告")
            return ""

        try:
            # 生成输出路径
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(
                    self.video_tracker.base_dir,
                    f"video_allocation_report_{timestamp}.json"
                )

            # 收集报告数据
            report_data = {
                "report_time": datetime.now().isoformat(),
                "statistics": self.video_tracker.get_allocation_statistics(),
                "videos": self.video_tracker.allocation_data["videos"],
                "accounts": self.video_tracker.allocation_data["accounts"]
            }

            # 保存报告
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)

            self.log(f"✅ 视频分配报告已导出到: {output_path}")
            return output_path

        except Exception as e:
            self.log(f"❌ 导出视频分配报告失败: {str(e)}")
            return ""

    def _find_cookie_file(self, account: str) -> Optional[str]:
        """
        查找账号的Cookie文件，只支持账号名.txt格式

        Args:
            account: 账号名称

        Returns:
            Cookie文件路径，如果未找到则返回None
        """
        # 只查找账号名.txt格式
        cookie_path = os.path.join(self.account_dir, f"{account}.txt")
        if os.path.exists(cookie_path):
            return cookie_path

        self.log(f"未找到账号 {account} 的Cookie文件")
        return None