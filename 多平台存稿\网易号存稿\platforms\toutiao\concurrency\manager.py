"""
头条号平台并发管理器 - 负责管理多账号并发处理
基于网易号并发管理器，适配头条号平台特殊需求
"""

import os
import time
import queue
import random
import threading
import traceback
from typing import List, Dict, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

# 尝试相对导入，如果失败则使用绝对导入
try:
    from .worker import ToutiaoWorker
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    from worker import ToutiaoWorker


class ToutiaoConcurrentManager:
    """头条号并发管理器类，负责管理多账号并发处理"""

    def __init__(self,
                 account_dir: str,
                 processed_dir: str,
                 processed_covers_dir: str,
                 archive_completed: bool = True,
                 headless_mode: bool = False,
                 draft_limit: int = 0,
                 loop_limit: int = 0,
                 log_callback: Callable = None,
                 screenshots_dir: str = None,
                 max_workers: int = 5,
                 random_video_allocation: bool = True):
        """
        初始化头条号并发管理器

        Args:
            account_dir: 账号目录
            processed_dir: 已处理视频目录
            processed_covers_dir: 已处理封面目录
            archive_completed: 是否归档已完成视频
            headless_mode: 是否使用无头模式
            draft_limit: 存稿数量限制，0表示不限制
            loop_limit: 循环次数限制，0表示不限制
            log_callback: 日志回调函数
            screenshots_dir: 截图保存目录
            max_workers: 最大工作线程数
            random_video_allocation: 是否随机分配视频，True为随机分配，False为顺序分配
        """
        self.account_dir = account_dir
        self.processed_dir = processed_dir
        self.processed_covers_dir = processed_covers_dir
        self.archive_completed = archive_completed
        self.headless_mode = headless_mode
        self.draft_limit = draft_limit
        self.loop_limit = loop_limit
        self.max_workers = max_workers
        self.log_callback = log_callback
        self.screenshots_dir = screenshots_dir
        self.progress_callback = None
        self.random_video_allocation = random_video_allocation

        # 初始化状态
        self.is_running = False
        self.executor = None
        self.futures = {}
        self.video_queue = queue.Queue()
        self.account_progress = {}

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def start(self, accounts: List[str]) -> bool:
        """
        开始并发处理

        Args:
            accounts: 账号列表

        Returns:
            是否成功启动
        """
        if self.is_running:
            self.log("头条号任务已在运行中")
            return False

        if not accounts:
            self.log("没有指定要处理的头条号账号")
            return False

        self.is_running = True

        try:
            # 初始化线程池
            self.executor = ThreadPoolExecutor(max_workers=min(self.max_workers, len(accounts)))

            # 初始化账号进度
            for account in accounts:
                self.account_progress[account] = {
                    "progress": 0,
                    "status": "等待中",
                    "last_update": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "details": "准备处理头条号账号"
                }

            # 准备视频队列
            self._prepare_video_queue()

            # 提交任务
            self.futures = {}
            for account in accounts:
                future = self.executor.submit(self._process_account, account)
                self.futures[future] = account

            # 启动监控线程
            threading.Thread(target=self._monitor_progress, daemon=True).start()

            self.log(f"已启动 {len(accounts)} 个头条号账号的并发处理")
            return True

        except Exception as e:
            self.log(f"启动头条号并发处理失败: {str(e)}")
            self.is_running = False
            return False

    def stop(self) -> None:
        """停止并发处理"""
        if not self.is_running:
            return

        self.log("正在停止头条号并发处理...")
        self.is_running = False

        # 停止所有工作线程
        if self.executor:
            self.executor.shutdown(wait=False)

        # 取消所有未完成的任务
        for future in self.futures:
            future.cancel()

        self.log("头条号并发处理已停止")

    def get_progress(self) -> Dict[str, Any]:
        """
        获取处理进度

        Returns:
            进度信息字典
        """
        return {
            "is_running": self.is_running,
            "account_progress": self.account_progress.copy(),
            "total_accounts": len(self.account_progress),
            "completed_accounts": sum(1 for p in self.account_progress.values() if p["status"] in ["完成", "失败"])
        }

    def set_progress_callback(self, callback: Callable) -> None:
        """
        设置进度回调函数

        Args:
            callback: 进度回调函数
        """
        self.progress_callback = callback

    def _prepare_video_queue(self) -> None:
        """准备视频队列"""
        try:
            # 获取所有视频文件
            video_files = []
            if os.path.exists(self.processed_dir):
                for file in os.listdir(self.processed_dir):
                    if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv')):
                        video_files.append(os.path.join(self.processed_dir, file))

            # 随机打乱视频顺序（如果启用）
            if self.random_video_allocation:
                random.shuffle(video_files)

            # 将视频添加到队列
            for video_path in video_files:
                self.video_queue.put(video_path)

            # 诊断信息：打印扫描目录与前几个文件名
            preview = ", ".join(os.path.basename(p) for p in video_files[:5])
            self.log(f"视频扫描目录: {self.processed_dir}")
            self.log(f"已准备 {len(video_files)} 个视频文件到队列" + (f"，示例: {preview}" if preview else ""))

        except Exception as e:
            self.log(f"准备视频队列失败: {str(e)}")

    def _process_account(self, account: str) -> bool:
        """
        处理单个账号

        Args:
            account: 账号名称

        Returns:
            处理结果
        """
        try:
            self.log(f"开始处理头条号账号: {account}")

            # 更新进度
            self._update_account_progress(account, 0, "初始化", "正在初始化头条号处理器")

            # 创建头条号工作线程
            worker = ToutiaoWorker(
                account=account,
                account_dir=self.account_dir,
                processed_dir=self.processed_dir,
                processed_covers_dir=self.processed_covers_dir,
                video_queue=self.video_queue,
                archive_completed=self.archive_completed,
                headless_mode=self.headless_mode,
                draft_limit=self.draft_limit,
                loop_limit=self.loop_limit,
                log_callback=lambda msg: self._account_log(account, msg),
                progress_callback=lambda progress, status, details: 
                    self._update_account_progress(account, progress, status, details),
                screenshots_dir=self.screenshots_dir
            )

            # 执行处理
            result = worker.run()

            # 更新最终状态
            if result:
                self._update_account_progress(account, 100, "完成", "头条号账号处理完成")
                self.log(f"✅ 头条号账号 {account} 处理完成")
            else:
                self._update_account_progress(account, 0, "失败", "头条号账号处理失败")
                self.log(f"❌ 头条号账号 {account} 处理失败")

            return result

        except Exception as e:
            error_msg = f"处理头条号账号 {account} 时发生异常: {str(e)}"
            self.log(f"❌ {error_msg}")
            self._update_account_progress(account, 0, "异常", error_msg)
            traceback.print_exc()
            return False

    def _update_account_progress(self, account: str, progress: int, status: str, details: str) -> None:
        """
        更新账号进度

        Args:
            account: 账号名称
            progress: 进度百分比
            status: 状态
            details: 详细信息
        """
        if account in self.account_progress:
            self.account_progress[account].update({
                "progress": progress,
                "status": status,
                "details": details,
                "last_update": time.strftime("%Y-%m-%d %H:%M:%S")
            })

            # 调用进度回调
            if self.progress_callback:
                try:
                    self.progress_callback(self.get_progress())
                except Exception as e:
                    self.log(f"进度回调异常: {str(e)}")

    def _account_log(self, account: str, message: str) -> None:
        """
        账号专用日志记录

        Args:
            account: 账号名称
            message: 日志消息
        """
        # 过滤重要日志信息
        important_keywords = [
            "开始处理", "处理完成", "登录成功", "登录失败", "存稿成功", "存稿失败",
            "上传成功", "上传失败", "错误", "异常", "完成", "失败"
        ]

        # 检查是否是重要日志
        is_important = any(keyword in message for keyword in important_keywords)

        # 如果是重要日志，直接显示
        if is_important:
            self.log(f"[头条号-{account}] {message}")
        else:
            # 对于非重要日志，进行过滤，但保留“创建浏览器 分配端口”类关键信息
            technical_keywords = [
                "Cookie", "浏览器", "URL", "端口", "解析", "验证", "添加",
                "设置域名", "驱动", "页面", "元素", "脚本", "iframe", "文本",
                "文件", "导航", "监控", "检测", "超时"
            ]

            # 如果包含创建浏览器分配端口信息，强制输出
            if "创建浏览器" in message and "端口" in message:
                self.log(f"[头条号-{account}] {message}")
            else:
                # 如果消息不包含任何技术关键词，则显示
                if not any(keyword in message for keyword in technical_keywords):
                    self.log(f"[头条号-{account}] {message}")

    def _monitor_progress(self) -> None:
        """监控处理进度"""
        while self.is_running:
            try:
                # 检查是否所有任务都已完成
                all_completed = all(
                    future.done() for future in self.futures
                )

                if all_completed:
                    self.log("所有头条号账号处理完成")
                    self.is_running = False
                    break

                # 等待一段时间再检查
                time.sleep(2)

            except Exception as e:
                self.log(f"监控进度时发生异常: {str(e)}")
                break
