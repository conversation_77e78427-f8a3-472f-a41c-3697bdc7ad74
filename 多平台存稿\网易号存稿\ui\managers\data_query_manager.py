#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据查询管理器 - 负责账号数据查询相关功能
遵循MECE原则：与数据查询相关的所有功能集中管理
"""

import threading
from typing import Dict, Any, Optional, List
from concurrent.futures import ThreadPoolExecutor, as_completed

from 网易号存稿.common.logger import logger
from 网易号存稿.common.constants import AccountStatus


class DataQueryManager:
    """数据查询管理器 - 负责所有数据查询相关功能"""

    def __init__(self, parent_ui, config_manager, account_manager, account_data):
        """
        初始化数据查询管理器

        Args:
            parent_ui: 主UI实例
            config_manager: 配置管理器
            account_manager: 账号管理器
            account_data: 账号数据处理器
        """
        self.parent_ui = parent_ui
        self.config_manager = config_manager
        self.account_manager = account_manager
        self.account_data = account_data

        # 查询状态
        self.is_querying = False
        self.query_thread = None

    def start_data_query(self):
        """开始数据查询"""
        # 检查是否已经在查询中
        if self.is_querying:
            self.parent_ui.log("⚠️ 数据查询已在进行中，请等待完成")
            return

        # 启动计时器和重置统计
        self.parent_ui.start_runtime_timer()
        self.parent_ui.reset_success_fail_stats()

        # 根据当前平台选择查询方法
        current_platform = self.parent_ui.current_platform

        if current_platform == "netease":
            self._start_netease_data_query()
        elif current_platform == "toutiao":
            self._start_toutiao_data_query()
        elif current_platform == "dayu":
            self._start_dayu_data_query()
        else:
            self.parent_ui.log(f"❌ 不支持的平台: {current_platform}")

    def start_single_account_query(self, account: str):
        """开始单账号数据查询

        Args:
            account: 要查询的账号
        """
        # 检查是否已经在查询中
        if self.is_querying:
            self.parent_ui.log("⚠️ 数据查询已在进行中，请等待完成")
            return

        # 启动计时器和重置统计
        self.parent_ui.start_runtime_timer()
        self.parent_ui.reset_success_fail_stats()

        # 根据当前平台选择查询方法
        current_platform = self.parent_ui.current_platform

        if current_platform == "netease":
            self._start_single_netease_data_query(account)
        elif current_platform == "toutiao":
            self._start_single_toutiao_data_query(account)
        elif current_platform == "dayu":
            self._start_single_dayu_data_query(account)
        else:
            self.parent_ui.log(f"❌ 不支持的平台: {current_platform}")

    def _start_netease_data_query(self):
        """开始网易号数据查询"""
        self.parent_ui.log("开始查询所有网易号账号数据")

        # 获取账号列表
        accounts = self.account_manager.load_accounts()
        if not accounts:
            self.parent_ui.log("❌ 没有找到账号，请先添加账号")
            return

        # 启动查询线程
        self.query_thread = threading.Thread(
            target=self._query_data_thread,
            args=(accounts,),
            daemon=True
        )
        self.query_thread.start()

    def _start_dayu_data_query(self):
        """开始大鱼号数据查询"""
        self.parent_ui.log("开始查询所有大鱼号账号数据")

        # 获取账号列表
        accounts = self.account_manager.load_accounts()
        if not accounts:
            self.parent_ui.log("❌ 没有找到账号，请先添加账号")
            return

        # 启动查询线程
        self.query_thread = threading.Thread(
            target=self._query_dayu_data_thread,
            args=(accounts,),
            daemon=True
        )
        self.query_thread.start()

    def _start_toutiao_data_query(self):
        """开始头条数据查询"""
        self.parent_ui.log("开始查询所有头条账号数据")

        # 获取账号列表
        accounts = self.account_manager.load_accounts()
        if not accounts:
            self.parent_ui.log("❌ 没有找到账号，请先添加账号")
            return

        # 启动查询线程
        self.query_thread = threading.Thread(
            target=self._query_toutiao_data_thread,
            args=(accounts,),
            daemon=True
        )
        self.query_thread.start()

    def _start_single_netease_data_query(self, account: str):
        """开始单个网易号账号数据查询"""
        self.parent_ui.log(f"开始查询网易号账号: {account}")

        # 启动查询线程
        self.query_thread = threading.Thread(
            target=self._query_single_netease_data_thread,
            args=(account,),
            daemon=True
        )
        self.query_thread.start()

    def _start_single_toutiao_data_query(self, account: str):
        """开始单个头条号账号数据查询"""
        self.parent_ui.log(f"开始查询头条号账号: {account}")

        # 启动查询线程
        self.query_thread = threading.Thread(
            target=self._query_single_toutiao_data_thread,
            args=(account,),
            daemon=True
        )
        self.query_thread.start()

    def _start_single_dayu_data_query(self, account: str):
        """开始单个大鱼号账号数据查询"""
        self.parent_ui.log(f"开始查询大鱼号账号: {account}")

        # 启动查询线程
        self.query_thread = threading.Thread(
            target=self._query_single_dayu_data_thread,
            args=(account,),
            daemon=True
        )
        self.query_thread.start()

    def _query_toutiao_data_thread(self, accounts):
        """头条数据查询线程 - 支持多线程并发"""
        try:
            self.is_querying = True
            self.parent_ui.log(f"开始查询 {len(accounts)} 个头条账号的数据")

            # 导入头条数据查询管理器
            from 网易号存稿.platforms.toutiao.data_query import ToutiaoDataQueryManager

            # 获取账号目录
            if self.account_manager and hasattr(self.account_manager, 'account_dir'):
                account_dir = self.account_manager.account_dir
            else:
                account_dir = self.config_manager.get("account_dir", "accounts", platform="toutiao")

            # 获取无头模式设置
            headless_mode = self.config_manager.get("query_headless_mode", True, platform="toutiao")

            # 创建数据查询管理器
            data_query = ToutiaoDataQueryManager(
                account_dir=account_dir,
                log_callback=self.parent_ui.log,
                headless=headless_mode
            )

            # 数据更新回调函数 - 使用串行刷新机制
            def data_update_callback(account, result):
                # 添加类型检查，确保result是字典
                if result and isinstance(result, dict) and result.get("状态") == AccountStatus.SUCCESS:
                    self.parent_ui.log(f"✅ {account} 数据已更新")
                    # 使用串行刷新队列处理UI更新
                    self.parent_ui.queue_account_update(account, result)

                    # 更新账号管理器状态为正常
                    if hasattr(self.parent_ui, 'account_manager') and self.parent_ui.account_manager:
                        self.parent_ui.account_manager.update_account_status(account, "正常", "数据查询成功")
                elif result and not isinstance(result, dict):
                    # 如果result不是字典，记录错误
                    self.parent_ui.log(f"❌ 账号 {account} 返回了无效的结果类型: {type(result)}")
                    self.parent_ui.log(f"❌ 结果内容: {result}")

                    # 更新账号管理器状态为查询失败
                    if hasattr(self.parent_ui, 'account_manager') and self.parent_ui.account_manager:
                        self.parent_ui.account_manager.update_account_status(account, AccountStatus.QUERY_FAILED, f"返回无效结果类型: {type(result)}")
                else:
                    # 查询失败的情况
                    if result and isinstance(result, dict):
                        failure_status = result.get("状态", AccountStatus.QUERY_FAILED)
                        reason = result.get("错误信息", "未知错误")
                    else:
                        failure_status = AccountStatus.QUERY_FAILED
                        reason = "查询失败"
                    self.parent_ui.log(f"❌ {account} 数据查询失败: {reason}")

                    # 同步更新账号管理器状态
                    if hasattr(self.parent_ui, 'account_manager') and self.parent_ui.account_manager:
                        self.parent_ui.account_manager.update_account_status(account, failure_status, reason)

                # 实时更新成功/失败统计
                try:
                    if result and isinstance(result, dict) and result.get("状态") == AccountStatus.SUCCESS:
                        success_now = getattr(self.parent_ui, 'current_success_count', 0) + 1
                        fail_now = getattr(self.parent_ui, 'current_fail_count', 0)
                    else:
                        success_now = getattr(self.parent_ui, 'current_success_count', 0)
                        fail_now = getattr(self.parent_ui, 'current_fail_count', 0) + 1
                    self.parent_ui.update_success_fail_stats(success_now, fail_now)
                except Exception:
                    pass


            # 进度回调函数 - 更新侧边栏任务进度
            def progress_callback(completed, total, current_account):
                # 更新侧边栏任务进度 - 修复lambda变量绑定问题
                self.parent_ui.root.after(0, lambda c=completed, t=total: self.parent_ui.update_task_progress(c, t))
                self.parent_ui.root.after(0, lambda acc=current_account: self.parent_ui.update_current_account_status(acc, "querying"))

            # 从配置中获取头条平台的查询设置
            max_workers = self.config_manager.get("query_max_threads", 10, platform="toutiao")
            batch_size = self.config_manager.get("query_batch_size", 10, platform="toutiao")

            # 限制并发数不超过账号数量
            max_workers = min(max_workers, len(accounts))

            self.parent_ui.log(f"📊 并发设置: 最大线程数={max_workers}, 批大小={batch_size}")

            # 重置任务进度
            self.parent_ui.update_task_progress(0, len(accounts))

            # 执行并发查询
            results = data_query.query_accounts_concurrent(
                accounts,
                max_workers=max_workers,
                data_callback=data_update_callback,
                progress_callback=progress_callback
            )

            # 统计结果 - 修复：results是字典，需要遍历其值
            success_count = sum(1 for result in results.values() if result and isinstance(result, dict) and result.get("状态") == AccountStatus.SUCCESS)
            fail_count = len(accounts) - success_count

            self.parent_ui.log(f"✅ 头条数据查询完成！成功: {success_count}, 失败: {fail_count}")

            # 不需要全表刷新，因为每个账号已经通过增量更新处理了
            # self.parent_ui.root.after(0, self.parent_ui.update_account_tree)

        except Exception as e:
            error_msg = f"头条数据查询失败: {str(e)}"
            self.parent_ui.log(f"❌ {error_msg}")
            logger.error(error_msg)
        finally:
            self.is_querying = False
            # 重置状态显示
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_current_account_status("", "idle"))
            # 重置侧边栏运行状态为空闲
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_running_status("🟢 空闲", "#28A745"))

    def _query_data_thread(self, accounts):
        """网易号数据查询线程"""
        try:
            self.is_querying = True
            self.parent_ui.log(f"开始查询 {len(accounts)} 个网易号账号的数据")

            # 导入网易号数据查询管理器
            from 网易号存稿.data.query import NeteaseDataQueryManager

            # 创建数据查询管理器
            query_manager = NeteaseDataQueryManager(
                config_manager=self.config_manager,
                account_manager=self.account_manager,
                log_callback=self.parent_ui.log
            )

            # 进度回调函数 - 更新侧边栏任务进度
            def progress_callback(completed, total, current_account):
                # 更新侧边栏任务进度 - 修复lambda变量绑定问题
                self.parent_ui.root.after(0, lambda c=completed, t=total: self.parent_ui.update_task_progress(c, t))

            # 数据回调函数 - 使用串行刷新机制
            def data_callback(account, result):
                # 添加类型检查，确保result是字典
                if result and isinstance(result, dict) and result.get("状态") == AccountStatus.SUCCESS:
                    # 获取当前平台的数据文件路径
                    current_data_file = self.parent_ui._get_data_file_path()
                    self.parent_ui.log(f"✅ {account} 数据已更新")

                    # 创建临时的AccountData对象使用正确的数据文件
                    from 网易号存稿.account.data import AccountData
                    temp_account_data = AccountData(current_data_file, self.parent_ui.log)

                    # 立即更新账号数据到正确的文件
                    update_success = temp_account_data.update_account_data(account, result)
                    if update_success:
                        # 同步更新主界面的AccountData对象，确保数据一致性
                        if hasattr(self.parent_ui, 'account_data') and self.parent_ui.account_data:
                            if self.parent_ui.account_data.data_file == current_data_file:
                                # 如果是同一个文件，直接更新内存中的数据
                                self.parent_ui.account_data.account_data[account] = result.copy()

                        # 使用串行刷新队列处理UI更新
                        self.parent_ui.queue_account_update(account, result)

                        # 更新账号管理器状态为正常
                        if hasattr(self.parent_ui, 'account_manager') and self.parent_ui.account_manager:
                            self.parent_ui.account_manager.update_account_status(account, AccountStatus.NORMAL, "数据查询成功")
                    else:
                        self.parent_ui.log(f"❌ {account} 数据更新失败")
                else:
                    # 修复字段名：网易号返回"错误信息"而不是"原因"
                    if result and isinstance(result, dict):
                        failure_status = result.get("状态", AccountStatus.QUERY_FAILED)
                        reason = result.get("错误信息", "未知错误")
                    else:
                        failure_status = AccountStatus.QUERY_FAILED
                        reason = "查询失败"
                    self.parent_ui.log(f"❌ {account} 数据查询失败: {reason}")

                    # 同步更新账号管理器状态
                    if hasattr(self.parent_ui, 'account_manager') and self.parent_ui.account_manager:
                        self.parent_ui.account_manager.update_account_status(account, failure_status, reason)

            # 从配置中获取网易号平台的查询设置
            max_workers = self.config_manager.get("query_max_threads", 10, platform="netease")
            batch_size = self.config_manager.get("query_batch_size", 10, platform="netease")

            # 限制并发数不超过账号数量
            max_workers = min(max_workers, len(accounts))

            self.parent_ui.log(f"🚀 开始并发查询 {len(accounts)} 个网易号账号...")
            self.parent_ui.log(f"📊 并发设置: 最大线程数={max_workers}, 批大小={batch_size}")

            # 重置任务进度
            self.parent_ui.update_task_progress(0, len(accounts))

            # 使用多线程并发查询
            results = query_manager.query_accounts_concurrent(
                accounts=accounts,
                max_workers=max_workers,
                data_callback=data_callback,
                progress_callback=progress_callback
            )

            # 统计结果
            success_count = sum(1 for result in results.values()
                              if result and isinstance(result, dict) and result.get("状态") == AccountStatus.SUCCESS)
            fail_count = len(accounts) - success_count

            # 查询完成
            self.parent_ui.log(f"✅ 网易号数据查询完成！成功: {success_count}, 失败: {fail_count}")


            self.parent_ui.root.after(0, lambda total=len(accounts): self.parent_ui.update_task_progress(total, total))

        except Exception as e:
            error_msg = f"网易号数据查询失败: {str(e)}"
            self.parent_ui.log(f"❌ {error_msg}")
            logger.error(error_msg)
        finally:
            self.is_querying = False
            # 重置状态显示
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_current_account_status("", "idle"))
            # 重置侧边栏运行状态为空闲
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_running_status("🟢 空闲", "#28A745"))

    def _query_dayu_data_thread(self, accounts):
        """大鱼号数据查询线程"""
        try:
            self.is_querying = True
            self.parent_ui.log(f"开始查询 {len(accounts)} 个大鱼号账号的数据")

            # 导入大鱼号数据查询器
            from 网易号存稿.platforms.dayu.data_query import DayuDataQuery

            # 创建数据查询器
            data_query = DayuDataQuery(
                config_manager=self.config_manager,
                account_manager=self.account_manager
            )

            success_count = 0
            fail_count = 0

            for i, account in enumerate(accounts, 1):
                try:
                    self.parent_ui.log(f"正在查询大鱼号账号 {account} ({i}/{len(accounts)})")

                    # 更新进度显示 - 修复lambda变量绑定问题
                    self.parent_ui.root.after(0, lambda current=i-1, total=len(accounts): self.parent_ui.update_task_progress(current, total))
                    self.parent_ui.root.after(0, lambda acc=account: self.parent_ui.update_current_account_status(acc, "querying"))

                    # 查询账号数据
                    result = data_query.query_account_data(account)

                    # 添加类型检查，确保result是字典
                    if result and isinstance(result, dict) and result.get("状态") == AccountStatus.SUCCESS:
                        # 获取当前平台的数据文件路径
                        current_data_file = self.parent_ui._get_data_file_path()

                        # 创建临时的AccountData对象使用正确的数据文件
                        from 网易号存稿.account.data import AccountData
                        temp_account_data = AccountData(current_data_file, self.parent_ui.log)

                        # 更新账号数据到正确的文件
                        update_success = temp_account_data.update_account_data(account, result)
                        if update_success:
                            success_count += 1
                            self.parent_ui.log(f"✅ {account} 大鱼号数据已更新到文件: {current_data_file}")

                            # 同步更新主界面的AccountData对象，确保数据一致性
                            if hasattr(self.parent_ui, 'account_data') and self.parent_ui.account_data:
                                if self.parent_ui.account_data.data_file == current_data_file:
                                    # 如果是同一个文件，直接更新内存中的数据
                                    self.parent_ui.account_data.account_data[account] = result.copy()
                                    self.parent_ui.log(f"✅ 已同步更新主界面AccountData对象")
                        else:
                            fail_count += 1
                            self.parent_ui.log(f"❌ {account} 大鱼号数据更新失败")
                    else:
                        fail_count += 1
                        # 修复字段名：大鱼号返回"错误信息"而不是"原因"
                        if result and isinstance(result, dict):
                            reason = result.get("错误信息", "未知错误")
                        else:
                            reason = "查询失败"
                        self.parent_ui.log(f"❌ {account} 大鱼号数据查询失败: {reason}")

                    # 不需要全表刷新，因为每个账号已经通过增量更新处理了
                    # self.parent_ui.root.after(100, self.parent_ui.update_account_tree)

                except Exception as e:
                    fail_count += 1
                    error_msg = f"查询大鱼号账号 {account} 时发生错误: {str(e)}"
                    self.parent_ui.log(f"❌ {error_msg}")
                    logger.error(error_msg)

            # 查询完成
            self.parent_ui.log(f"✅ 大鱼号数据查询完成！成功: {success_count}, 失败: {fail_count}")


            self.parent_ui.root.after(0, lambda total=len(accounts): self.parent_ui.update_task_progress(total, total))

        except Exception as e:
            error_msg = f"大鱼号数据查询失败: {str(e)}"
            self.parent_ui.log(f"❌ {error_msg}")
            logger.error(error_msg)
        finally:
            self.is_querying = False
            # 重置状态显示
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_current_account_status("", "idle"))
            # 重置侧边栏运行状态为空闲
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_running_status("🟢 空闲", "#28A745"))

    def stop_query(self):
        """停止数据查询"""
        if self.is_querying:
            self.is_querying = False
            self.parent_ui.log("正在停止数据查询...")

    def _query_single_netease_data_thread(self, account: str):
        """单个网易号账号数据查询线程 - 与并发查询保持一致"""
        try:
            self.is_querying = True
            self.parent_ui.log(f"🔍 开始查询网易号账号: {account}")

            # 更新侧边栏运行状态为查询中
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_running_status("🔍 查询中", "#007BFF"))

            # 导入网易号数据查询管理器 - 与并发查询保持一致
            from 网易号存稿.data.query import NeteaseDataQueryManager

            # 创建数据查询管理器 - 与并发查询保持一致
            query_manager = NeteaseDataQueryManager(
                config_manager=self.config_manager,
                account_manager=self.account_manager,
                log_callback=self.parent_ui.log
            )

            # 进度回调函数 - 更新侧边栏任务进度
            def progress_callback(completed, total, current_account):
                # 更新侧边栏任务进度 - 修复lambda变量绑定问题
                self.parent_ui.root.after(0, lambda c=completed, t=total: self.parent_ui.update_task_progress(c, t))

            # 数据回调函数 - 与并发查询保持一致
            def data_callback(account_name, result):
                if result and isinstance(result, dict) and result.get("状态") == AccountStatus.SUCCESS:
                    # 获取当前平台的数据文件路径
                    current_data_file = self.parent_ui._get_data_file_path()

                    # 创建临时的AccountData对象使用正确的数据文件
                    from 网易号存稿.account.data import AccountData
                    temp_account_data = AccountData(current_data_file, self.parent_ui.log)

                    # 立即更新账号数据到正确的文件
                    update_success = temp_account_data.update_account_data(account_name, result)
                    if update_success:
                        self.parent_ui.log(f"✅ {account_name} 数据已更新到文件: {current_data_file}")

                        # 同步更新主界面的AccountData对象，确保数据一致性
                        if hasattr(self.parent_ui, 'account_data') and self.parent_ui.account_data:
                            if self.parent_ui.account_data.data_file == current_data_file:
                                # 如果是同一个文件，直接更新内存中的数据
                                self.parent_ui.account_data.account_data[account_name] = result.copy()
                                self.parent_ui.log(f"✅ 已同步更新主界面AccountData对象")

                        # 延迟更新UI显示，确保数据已保存
                        self.parent_ui.root.after(100, self.parent_ui.update_account_tree)
                        # 通知所有打开的数据查看器刷新
                        self.parent_ui.root.after(200, self.parent_ui.notify_data_viewers_refresh)
                    else:
                        self.parent_ui.log(f"❌ {account_name} 数据更新失败")
                else:
                    # 修复字段名：网易号返回"错误信息"而不是"原因"
                    if result and isinstance(result, dict):
                        reason = result.get("错误信息", "未知错误")
                    else:
                        reason = "查询失败"
                    self.parent_ui.log(f"❌ {account_name} 数据查询失败: {reason}")

            # 从配置中获取网易号平台的查询设置 - 与并发查询保持一致
            max_workers = self.config_manager.get("query_max_threads", 10, platform="netease")

            # 使用多线程并发查询方法查询单个账号 - 与并发查询保持一致
            results = query_manager.query_accounts_concurrent(
                accounts=[account],  # 只查询一个账号
                max_workers=1,  # 单线程
                data_callback=data_callback,
                progress_callback=progress_callback
            )

            # 检查查询结果
            if results and account in results:
                result = results[account]
                if result and result.get("状态") == AccountStatus.SUCCESS:
                    self.parent_ui.log(f"✅ 网易号账号 {account} 数据查询成功")
                    # 更新账号管理器状态为正常
                    if hasattr(self.parent_ui, 'account_manager') and self.parent_ui.account_manager:
                        self.parent_ui.account_manager.update_account_status(account, AccountStatus.NORMAL, "数据查询成功")
                else:
                    # 获取失败原因
                    failure_status = result.get("状态", "查询失败")
                    failure_reason = result.get("错误信息", "未知错误")
                    self.parent_ui.log(f"❌ 网易号账号 {account} 数据查询失败: {failure_reason}")

                    # 同步更新账号管理器状态
                    if hasattr(self.parent_ui, 'account_manager') and self.parent_ui.account_manager:
                        self.parent_ui.account_manager.update_account_status(account, failure_status, failure_reason)
            else:
                self.parent_ui.log(f"❌ 网易号账号 {account} 查询无结果")
                # 更新账号管理器状态为查询失败
                if hasattr(self.parent_ui, 'account_manager') and self.parent_ui.account_manager:
                    self.parent_ui.account_manager.update_account_status(account, AccountStatus.NO_RESULT, "查询无结果")

        except Exception as e:
            self.parent_ui.log(f"❌ 查询网易号账号 {account} 时出错: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            self.is_querying = False
            # 在主线程中更新界面
            self.parent_ui.root.after(0, self.parent_ui.update_account_tree)
            # 重置状态显示
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_current_account_status("", "idle"))
            # 重置侧边栏运行状态为空闲
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_running_status("🟢 空闲", "#28A745"))
            self.parent_ui.log(f"🔍 网易号账号 {account} 查询完成")

    def _query_single_toutiao_data_thread(self, account: str):
        """单个头条号账号数据查询线程 - 与并发查询保持一致"""
        try:
            self.is_querying = True
            self.parent_ui.log(f"🔍 开始查询头条号账号: {account}")

            # 更新侧边栏运行状态为查询中
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_running_status("🔍 查询中", "#007BFF"))

            # 导入头条数据查询管理器 - 与并发查询保持一致
            from 网易号存稿.platforms.toutiao.data_query import ToutiaoDataQueryManager

            # 获取账号目录 - 与并发查询保持一致
            if self.account_manager and hasattr(self.account_manager, 'account_dir'):
                account_dir = self.account_manager.account_dir
            else:
                account_dir = self.config_manager.get("account_dir", "accounts", platform="toutiao")

            # 获取无头模式设置 - 与并发查询保持一致
            headless_mode = self.config_manager.get("query_headless_mode", True, platform="toutiao")

            # 创建数据查询管理器 - 与并发查询保持一致
            data_query = ToutiaoDataQueryManager(
                account_dir=account_dir,
                log_callback=self.parent_ui.log,
                headless=headless_mode
            )

            # 数据更新回调函数 - 使用串行刷新机制
            def data_update_callback(account_name, result):
                if result and isinstance(result, dict) and result.get("状态") == AccountStatus.SUCCESS:
                    self.parent_ui.log(f"✅ {account_name} 数据已更新")
                    # 使用串行刷新队列处理UI更新
                    self.parent_ui.queue_account_update(account_name, result)
                elif result and not isinstance(result, dict):
                    self.parent_ui.log(f"❌ 账号 {account_name} 返回了无效的结果类型: {type(result)}")
                else:
                    # 查询失败的情况
                    reason = result.get("错误信息", "未知错误") if result and isinstance(result, dict) else "查询失败"
                    self.parent_ui.log(f"❌ {account_name} 数据查询失败: {reason}")

            # 进度回调函数 - 更新侧边栏任务进度
            def progress_callback(completed, total, current_account):
                # 更新侧边栏任务进度 - 修复lambda变量绑定问题
                self.parent_ui.root.after(0, lambda c=completed, t=total: self.parent_ui.update_task_progress(c, t))
                self.parent_ui.root.after(0, lambda acc=current_account: self.parent_ui.update_current_account_status(acc, "querying"))

            # 使用并发查询方法查询单个账号 - 与并发查询保持一致
            results = data_query.query_accounts_concurrent(
                [account],  # 只查询一个账号
                max_workers=1,  # 单线程
                data_callback=data_update_callback,
                progress_callback=progress_callback
            )

            # 检查查询结果
            if results and account in results:
                result = results[account]
                if result and result.get("状态") == AccountStatus.SUCCESS:
                    self.parent_ui.log(f"✅ 头条号账号 {account} 数据查询成功")
                    # 更新账号管理器状态为正常
                    if hasattr(self.parent_ui, 'account_manager') and self.parent_ui.account_manager:
                        self.parent_ui.account_manager.update_account_status(account, AccountStatus.NORMAL, "数据查询成功")
                else:
                    # 获取失败原因
                    failure_status = result.get("状态", "查询失败")
                    failure_reason = result.get("错误信息", "未知错误")
                    self.parent_ui.log(f"❌ 头条号账号 {account} 数据查询失败: {failure_reason}")

                    # 同步更新账号管理器状态
                    if hasattr(self.parent_ui, 'account_manager') and self.parent_ui.account_manager:
                        self.parent_ui.account_manager.update_account_status(account, failure_status, failure_reason)
            else:
                self.parent_ui.log(f"❌ 头条号账号 {account} 查询无结果")
                # 更新账号管理器状态为查询失败
                if hasattr(self.parent_ui, 'account_manager') and self.parent_ui.account_manager:
                    self.parent_ui.account_manager.update_account_status(account, AccountStatus.NO_RESULT, "查询无结果")

        except Exception as e:
            self.parent_ui.log(f"❌ 查询头条号账号 {account} 时出错: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            self.is_querying = False
            # 不需要全表刷新，因为账号已经通过增量更新处理了
            # self.parent_ui.root.after(0, self.parent_ui.update_account_tree)
            # 重置状态显示
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_current_account_status("", "idle"))
            # 重置侧边栏运行状态为空闲
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_running_status("🟢 空闲", "#28A745"))
            self.parent_ui.log(f"🔍 头条号账号 {account} 查询完成")

    def _query_single_dayu_data_thread(self, account: str):
        """单个大鱼号账号数据查询线程 - 与多账号查询保持一致"""
        try:
            self.is_querying = True
            self.parent_ui.log(f"🔍 开始查询大鱼号账号: {account}")

            # 更新侧边栏运行状态为查询中
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_running_status("🔍 查询中", "#007BFF"))

            # 导入大鱼号数据查询器 - 与多账号查询保持一致
            from 网易号存稿.platforms.dayu.data_query import DayuDataQuery

            # 创建数据查询器 - 与多账号查询保持一致
            data_query = DayuDataQuery(
                config_manager=self.config_manager,
                account_manager=self.account_manager
            )

            # 更新进度显示 - 与多账号查询保持一致，修复lambda变量绑定问题
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_task_progress(0, 1))
            self.parent_ui.root.after(0, lambda acc=account: self.parent_ui.update_current_account_status(acc, "querying"))

            # 查询账号数据 - 与多账号查询保持一致
            result = data_query.query_account_data(account)

            # 添加类型检查，确保result是字典 - 与多账号查询保持一致
            if result and isinstance(result, dict) and result.get("状态") == AccountStatus.SUCCESS:
                # 获取当前平台的数据文件路径 - 与多账号查询保持一致
                current_data_file = self.parent_ui._get_data_file_path()

                # 创建临时的AccountData对象使用正确的数据文件 - 与多账号查询保持一致
                from 网易号存稿.account.data import AccountData
                temp_account_data = AccountData(current_data_file, self.parent_ui.log)

                # 立即更新账号数据到正确的文件 - 与多账号查询保持一致
                update_success = temp_account_data.update_account_data(account, result)
                if update_success:
                    self.parent_ui.log(f"✅ {account} 数据已更新")

                    # 同步更新主界面的AccountData对象，确保数据一致性
                    if hasattr(self.parent_ui, 'account_data') and self.parent_ui.account_data:
                        if self.parent_ui.account_data.data_file == current_data_file:
                            # 如果是同一个文件，直接更新内存中的数据
                            self.parent_ui.account_data.account_data[account] = result.copy()

                    # 使用串行刷新队列处理UI更新
                    self.parent_ui.queue_account_update(account, result)
                else:
                    self.parent_ui.log(f"❌ {account} 数据更新失败")

                self.parent_ui.log(f"✅ 大鱼号账号 {account} 数据查询成功")
            else:
                reason = result.get("错误信息", "未知错误") if result and isinstance(result, dict) else "查询失败"
                self.parent_ui.log(f"❌ 大鱼号账号 {account} 数据查询失败: {reason}")

            # 更新进度显示为完成 - 与多账号查询保持一致
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_task_progress(1, 1))

        except Exception as e:
            self.parent_ui.log(f"❌ 查询大鱼号账号 {account} 时出错: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            self.is_querying = False
            # 在主线程中更新界面
            self.parent_ui.root.after(0, self.parent_ui.update_account_tree)
            # 重置状态显示 - 与多账号查询保持一致
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_current_account_status("", "idle"))
            # 重置侧边栏运行状态为空闲
            self.parent_ui.root.after(0, lambda: self.parent_ui.update_running_status("🟢 空闲", "#28A745"))
            self.parent_ui.log(f"🔍 大鱼号账号 {account} 查询完成")

    def cleanup(self):
        """清理数据查询管理器资源"""
        try:
            self.stop_query()
            if self.query_thread and self.query_thread.is_alive():
                self.query_thread.join(timeout=2)
        except Exception as e:
            logger.error(f"清理数据查询管理器失败: {e}")
